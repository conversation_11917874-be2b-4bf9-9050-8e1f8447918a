import {
    mockDate,
    mockFriend,
    mockFriendRequest,
    mockFriendship,
    mockFriendshipWithFriend,
    mockRival,
    mockRivalWithDetails,
    mockRivalWithUser,
    mockUserPrivacySettings,
} from "./__mocks__/mockData.js";
import * as socialService from "../social.controller.js";
import * as SocialRepository from "../../../repositories/social.repository.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { LogErrorStack } from "../../../utils/log.js";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock the repository and logger
vi.mock("../../../repositories/social.repository.js");
vi.mock("../../../repositories/user.repository.js");

describe("Social Service", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("getFriendsList", () => {
        it("should return friends list successfully", async () => {
            const mockFriends = [mockFriend];
            vi.mocked(SocialRepository.getUserFriends).mockResolvedValue(mockFriends);

            const result = await socialService.getFriendsList(1);

            expect(SocialRepository.getUserFriends).toHaveBeenCalledWith(1);
            expect(result).toEqual({ data: mockFriends });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.getUserFriends).mockRejectedValue(error);

            const result = await socialService.getFriendsList(1);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to fetch friends list", statusCode: 500 });
        });
    });

    describe("getFriendRequests", () => {
        it("should return friend requests successfully", async () => {
            const mockRequests = [mockFriendRequest];
            vi.mocked(SocialRepository.getFriendRequests).mockResolvedValue(mockRequests);

            const result = await socialService.getFriendRequests(1);

            expect(SocialRepository.getFriendRequests).toHaveBeenCalledWith(1);
            expect(result).toEqual({ data: mockRequests });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.getFriendRequests).mockRejectedValue(error);

            const result = await socialService.getFriendRequests(1);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to fetch friend requests", statusCode: 500 });
        });
    });

    describe("sendFriendRequest", () => {
        it("should send friend request successfully", async () => {
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendRequest).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);
            vi.mocked(SocialRepository.createFriendRequest).mockResolvedValue(mockFriendRequest);

            const result = await socialService.sendFriendRequest(1, 2);

            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingFriendRequest).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.createFriendRequest).toHaveBeenCalledWith(1, 2);
            expect(result).toEqual({ data: mockFriendRequest });
        });

        it("should return error if users are already friends", async () => {
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(mockFriendship);

            const result = await socialService.sendFriendRequest(1, 2);

            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.createFriendRequest).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "You are already friends with this user", statusCode: 400 });
        });

        it("should return error if friend request already exists", async () => {
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendRequest).mockResolvedValue(mockFriendRequest);

            const result = await socialService.sendFriendRequest(1, 2);

            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingFriendRequest).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.createFriendRequest).not.toHaveBeenCalled();
            expect(result).toEqual({
                error: "A friend request already exists between you and this user",
                statusCode: 400,
            });
        });

        it("should return error if users are rivals", async () => {
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendRequest).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(mockRival);

            const result = await socialService.sendFriendRequest(1, 2);

            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingFriendRequest).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.createFriendRequest).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "You cannot send a friend request to your rival", statusCode: 400 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findExistingFriendship).mockRejectedValue(error);

            const result = await socialService.sendFriendRequest(1, 2);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to send friend request", statusCode: 500 });
        });
    });

    describe("respondToFriendRequest", () => {
        it("should accept friend request successfully", async () => {
            vi.mocked(SocialRepository.findFriendRequestById).mockResolvedValue(mockFriendRequest);
            vi.mocked(SocialRepository.deleteFriendRequest).mockResolvedValue(mockFriendRequest);
            vi.mocked(SocialRepository.createFriendship).mockResolvedValue({ id: 1 } as any);
            vi.mocked(SocialRepository.getFriendshipDetails).mockResolvedValue(mockFriendshipWithFriend);

            const result = await socialService.respondToFriendRequest(1, 1, true);

            expect(SocialRepository.findFriendRequestById).toHaveBeenCalledWith(1, 1);
            expect(SocialRepository.deleteFriendRequest).toHaveBeenCalledWith(1);
            expect(SocialRepository.createFriendship).toHaveBeenCalledTimes(2);
            expect(SocialRepository.getFriendshipDetails).toHaveBeenCalledWith(1, 2);
            expect(result).toEqual({ data: { accepted: true, friendship: mockFriendshipWithFriend } });
        });

        it("should decline friend request successfully", async () => {
            vi.mocked(SocialRepository.findFriendRequestById).mockResolvedValue(mockFriendRequest);
            vi.mocked(SocialRepository.deleteFriendRequest).mockResolvedValue(mockFriendRequest);

            const result = await socialService.respondToFriendRequest(1, 1, false);

            expect(SocialRepository.findFriendRequestById).toHaveBeenCalledWith(1, 1);
            expect(SocialRepository.deleteFriendRequest).toHaveBeenCalledWith(1);
            expect(SocialRepository.createFriendship).not.toHaveBeenCalled();
            expect(result).toEqual({ data: { accepted: false, friendship: null } });
        });

        it("should return error if request not found", async () => {
            vi.mocked(SocialRepository.findFriendRequestById).mockResolvedValue(null);

            const result = await socialService.respondToFriendRequest(1, 1, true);

            expect(SocialRepository.findFriendRequestById).toHaveBeenCalledWith(1, 1);
            expect(SocialRepository.deleteFriendRequest).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "Friend request not found", statusCode: 404 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findFriendRequestById).mockRejectedValue(error);

            const result = await socialService.respondToFriendRequest(1, 1, true);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to process friend request", statusCode: 500 });
        });
    });

    describe("removeFriend", () => {
        it("should remove friend successfully", async () => {
            vi.mocked(SocialRepository.deleteFriendships).mockResolvedValue({ count: 2 } as any);

            const result = await socialService.removeFriend(1, 2);

            expect(SocialRepository.deleteFriendships).toHaveBeenCalledWith(1, 2);
            expect(result).toEqual({ data: { removed: true } });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.deleteFriendships).mockRejectedValue(error);

            const result = await socialService.removeFriend(1, 2);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to remove friend", statusCode: 500 });
        });
    });

    describe("updateFriendNote", () => {
        it("should update friend note successfully", async () => {
            const updatedFriendship = { ...mockFriendship, note: "New note" };

            vi.mocked(SocialRepository.findFriendship).mockResolvedValue(mockFriendship);
            vi.mocked(SocialRepository.updateFriendshipNote).mockResolvedValue(updatedFriendship);

            const result = await socialService.updateFriendNote(1, 2, "New note");

            expect(SocialRepository.findFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.updateFriendshipNote).toHaveBeenCalledWith(1, "New note");
            expect(result).toEqual({ data: updatedFriendship });
        });

        it("should return error if friendship not found", async () => {
            vi.mocked(SocialRepository.findFriendship).mockResolvedValue(null);

            const result = await socialService.updateFriendNote(1, 2, "New note");

            expect(SocialRepository.findFriendship).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.updateFriendshipNote).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "Friend not found", statusCode: 404 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findFriendship).mockRejectedValue(error);

            const result = await socialService.updateFriendNote(1, 2, "New note");

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to update friend note", statusCode: 500 });
        });
    });

    describe("updateStatusMessage", () => {
        it("should update status message successfully", async () => {
            vi.mocked(SocialRepository.updateUserStatusMessage).mockResolvedValue({
                statusMessage: "New status",
                statusMessageUpdatedAt: mockDate,
            });

            const result = await socialService.updateStatusMessage(1, "New status");

            expect(SocialRepository.updateUserStatusMessage).toHaveBeenCalledWith(1, "New status");
            expect(result).toEqual({ data: { statusMessage: "New status", statusMessageUpdatedAt: mockDate } });
        });

        it("should handle null status message", async () => {
            vi.mocked(SocialRepository.updateUserStatusMessage).mockResolvedValue({
                statusMessage: null,
                statusMessageUpdatedAt: null,
            });

            const result = await socialService.updateStatusMessage(1, null);

            expect(SocialRepository.updateUserStatusMessage).toHaveBeenCalledWith(1, null);
            expect(result).toEqual({ data: { statusMessage: null, statusMessageUpdatedAt: null } });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.updateUserStatusMessage).mockRejectedValue(error);

            const result = await socialService.updateStatusMessage(1, "New status");

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to update status message", statusCode: 500 });
        });
    });

    describe("updatePrivacySettings", () => {
        it("should update privacy settings successfully", async () => {
            vi.mocked(SocialRepository.updateUserPrivacySettings).mockResolvedValue(mockUserPrivacySettings);

            const result = await socialService.updatePrivacySettings(1, false);

            expect(SocialRepository.updateUserPrivacySettings).toHaveBeenCalledWith(1, { showLastOnline: false });
            expect(result).toEqual({ data: mockUserPrivacySettings });
        });

        it("should return error when no settings provided", async () => {
            const result = await socialService.updatePrivacySettings(1);

            expect(SocialRepository.updateUserPrivacySettings).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "No privacy settings provided", statusCode: 400 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.updateUserPrivacySettings).mockRejectedValue(error);

            const result = await socialService.updatePrivacySettings(1, true);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to update privacy settings", statusCode: 500 });
        });
    });

    describe("getRivalsList", () => {
        it("should return rivals list successfully", async () => {
            const mockRivals = [mockRivalWithDetails];
            vi.mocked(SocialRepository.getUserRivals).mockResolvedValue(mockRivals);

            const result = await socialService.getRivalsList(1);

            expect(SocialRepository.getUserRivals).toHaveBeenCalledWith(1);
            expect(result).toEqual({ data: mockRivals });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.getUserRivals).mockRejectedValue(error);

            const result = await socialService.getRivalsList(1);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to fetch rivals list", statusCode: 500 });
        });
    });

    describe("addRival", () => {
        it("should add rival successfully", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(null);
            vi.mocked(UserRepository.checkUserExists).mockResolvedValue({ id: 2 });
            vi.mocked(SocialRepository.createRival).mockResolvedValue(mockRivalWithUser);

            const result = await socialService.addRival(1, 2);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(UserRepository.checkUserExists).toHaveBeenCalledWith(2);
            expect(SocialRepository.createRival).toHaveBeenCalledWith(1, 2);
            expect(result).toEqual({ data: mockRivalWithUser });
        });

        it("should return error if rival already exists", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(mockRival);

            const result = await socialService.addRival(1, 2);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.createRival).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "This user is already on your rivals list", statusCode: 400 });
        });

        it("should return error if users are friends", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(mockFriendship);

            const result = await socialService.addRival(1, 2);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 2);
            expect(UserRepository.checkUserExists).not.toHaveBeenCalled();
            expect(SocialRepository.createRival).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "You cannot add a friend as a rival", statusCode: 400 });
        });

        it("should return error if trying to add self as rival", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);

            const result = await socialService.addRival(1, 1);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 1);
            expect(UserRepository.checkUserExists).not.toHaveBeenCalled();
            expect(SocialRepository.createRival).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "You cannot add yourself as a rival", statusCode: 400 });
        });

        it("should return error if target user not found", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);
            vi.mocked(SocialRepository.findExistingFriendship).mockResolvedValue(null);
            vi.mocked(UserRepository.checkUserExists).mockResolvedValue(null);

            const result = await socialService.addRival(1, 999);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 999);
            expect(SocialRepository.findExistingFriendship).toHaveBeenCalledWith(1, 999);
            expect(UserRepository.checkUserExists).toHaveBeenCalledWith(999);
            expect(SocialRepository.createRival).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "User not found", statusCode: 404 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findExistingRival).mockRejectedValue(error);

            const result = await socialService.addRival(1, 2);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to add rival", statusCode: 500 });
        });
    });

    describe("removeRival", () => {
        it("should remove rival successfully", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(mockRival);
            vi.mocked(SocialRepository.deleteRival).mockResolvedValue(mockRival);

            const result = await socialService.removeRival(1, 2);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.deleteRival).toHaveBeenCalledWith(1);
            expect(result).toEqual({ data: { removed: true } });
        });

        it("should return error if rival not found", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);

            const result = await socialService.removeRival(1, 2);

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.deleteRival).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "Rival not found", statusCode: 404 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findExistingRival).mockRejectedValue(error);

            const result = await socialService.removeRival(1, 2);

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to remove rival", statusCode: 500 });
        });
    });

    describe("updateRivalNote", () => {
        it("should update rival note successfully", async () => {
            const updatedRival = { ...mockRival, note: "New note" };

            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(mockRival);
            vi.mocked(SocialRepository.updateRivalNote).mockResolvedValue(updatedRival);

            const result = await socialService.updateRivalNote(1, 2, "New note");

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.updateRivalNote).toHaveBeenCalledWith(1, "New note");
            expect(result).toEqual({ data: updatedRival });
        });

        it("should return error if rival not found", async () => {
            vi.mocked(SocialRepository.findExistingRival).mockResolvedValue(null);

            const result = await socialService.updateRivalNote(1, 2, "New note");

            expect(SocialRepository.findExistingRival).toHaveBeenCalledWith(1, 2);
            expect(SocialRepository.updateRivalNote).not.toHaveBeenCalled();
            expect(result).toEqual({ error: "Rival not found", statusCode: 404 });
        });

        it("should handle errors", async () => {
            const error = new Error("Database error");
            vi.mocked(SocialRepository.findExistingRival).mockRejectedValue(error);

            const result = await socialService.updateRivalNote(1, 2, "New note");

            expect(LogErrorStack).toHaveBeenCalled();
            expect(result).toEqual({ error: "Failed to update rival note", statusCode: 500 });
        });
    });
});
