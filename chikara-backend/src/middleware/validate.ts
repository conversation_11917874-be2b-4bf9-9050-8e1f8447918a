import { stringify } from "../utils/jsonHelper.js";
import { LogErrorStack } from "../utils/log.js";
import { NextFunction, Request, RequestHandler, Response } from "express";
import { Zod<PERSON>rror, z } from "zod";

interface ApiResponse<T = unknown> {
    success: boolean;
    data: T | null;
    error: string | null;
}

// // Helper type to merge inferred type with Request
// type TypedRequest<T> = Omit<Request, "body"> & { body: T };

/**
 * Middleware function that validates incoming request data against a provided Zod schema.
 * It parses the request body, query parameters, or path parameters based on the source and handles validation errors.
 *
 * @param schema - A Zod schema used for validating the request data.
 * @param source - Optional parameter to specify the source of data to validate: "query", "body", or "params"
 * @returns A RequestHandler that processes the request and sends appropriate responses.
 *
 * @throws ZodError - If the validation fails, it sends a 400 response with validation error details.
 * @throws Error - For unexpected errors, it logs the error and sends a 500 response.
 */
export const validate = <T extends z.ZodTypeAny>(
    schema: T,
    source?: "query" | "body" | "params"
): RequestHandler<{}, any, z.infer<T>, any> => {
    return (req: Request, res: Response, next: NextFunction) => {
        // Determine source based on provided parameter or HTTP method
        const dataSource = source || (req.method === "GET" ? "query" : "body");

        try {
            const data = schema.parse(req[dataSource]);
            if (dataSource === "body") {
                req[dataSource] = data;
            }
            next();
        } catch (error) {
            if (error instanceof ZodError) {
                const errorMessages = error.issues.map((issue) => ({
                    message: `${issue.path.join(".")} is ${issue.message}`,
                }));
                res.status(400).send(
                    stringify({ success: false, data: null, error: "Invalid data", validationErrors: errorMessages })
                );
                return;
            }

            const errorResponse: ApiResponse = {
                success: false,
                data: null,
                error: "Unexpected server error",
            };

            LogErrorStack({ error });
            res.status(500).send(stringify(errorResponse));
            return;
        }
    };
};

export default validate;
