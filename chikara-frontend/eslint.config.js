import js from "@eslint/js";
import pluginQuery from "@tanstack/eslint-plugin-query";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";
import { createTypeScriptImportResolver } from "eslint-import-resolver-typescript";
import importPlugin from "eslint-plugin-import-x";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import eslintPluginUnicorn from "eslint-plugin-unicorn";
import globals from "globals";
import tseslint from "typescript-eslint";

const reactConfig = {
    name: "react",
    settings: { react: { version: "detect" } },
    plugins: {
        react,
        "react-hooks": reactHooks,
        "react-refresh": reactRefresh,
    },
    rules: {
        ...react.configs.recommended.rules,
        ...react.configs["jsx-runtime"].rules,
        ...reactHooks.configs.recommended.rules,
        "react-hooks/react-compiler": "error",
        "react-refresh/only-export-components": ["warn", { allowConstantExport: true }],
        "react/prop-types": "off",
        "react/jsx-boolean-value": "error",
        "react/jsx-filename-extension": [2, { extensions: [".ts", ".tsx"] }],
        "react/jsx-sort-props": [
            "warn",
            {
                callbacksLast: true,
                shorthandFirst: true,
                reservedFirst: true,
                multiline: "last",
                noSortAlphabetically: true,
            },
        ],
        "react/no-unknown-property": "off",
        "react/react-in-jsx-scope": "off",
    },
};

const unicornConfig = {
    name: "unicorn",
    plugins: {
        unicorn: eslintPluginUnicorn,
    },
    rules: {
        "unicorn/custom-error-definition": "error",
        "unicorn/empty-brace-spaces": "error",
        "unicorn/no-array-for-each": "off",
        "unicorn/no-array-reduce": "off",
        "unicorn/no-console-spaces": "error",
        "unicorn/no-null": "off",
        "unicorn/filename-case": "off",
        "unicorn/prevent-abbreviations": "off",
    },
};

const typescriptConfig = {
    name: "typescript",
    files: ["**/*.ts", "**/*.tsx"],
    plugins: {
        "@typescript-eslint": typescriptEslint,
    },
    languageOptions: {
        parser: tsParser,
        ecmaVersion: "latest",
        sourceType: "module",
        globals: globals.browser,
        parserOptions: {
            project: "./tsconfig.json",
        },
    },
};

const baseRules = {
    rules: {
        "no-unneeded-ternary": "error",
        "prefer-const": "error",
        "require-await": "warn",
        "no-lonely-if": "error",
        "no-new": "error",
        "no-undef-init": "warn",
    },
};

const importSettings = {
    settings: {
        "import-x/resolver-next": [
            createTypeScriptImportResolver({
                project: "./tsconfig.json",
            }),
        ],
    },
};

export default tseslint.config(
    { ignores: ["dist"] },
    baseRules,
    typescriptConfig,
    importPlugin.flatConfigs.recommended,
    importSettings,
    unicornConfig,
    reactConfig,
    ...pluginQuery.configs["flat/recommended"],
    {
        settings: {
            tailwindcss: {
                callees: ["classNames", "clsx", "cn"],
            },
        },
    }
);
