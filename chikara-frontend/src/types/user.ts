import { AppRouterClient } from "@/lib/orpc";

export type User = Awaited<ReturnType<AppRouterClient["user"]["getCurrentUserInfo"]>>;
export type UserType = User["userType"];
// export type UserType = "admin" | "student" | "prefect";

export type UserStat = "strength" | "intelligence" | "dexterity" | "defence" | "endurance" | "vitality";

export interface TrainStatRequest {
    stat: UserStat;
    focusAmount: number;
}

export interface TrainStatResponse {
    statProgress: {
        stat: string;
        expGained: number;
        leveledUp: boolean;
        levelsGained: number;
        currentLevel: number;
        currentExp: number;
        expToNextLevel: number;
        previousLevel: number;
    };
    focusRemaining: number;
    dailyFatigueRemaining: number;
}
