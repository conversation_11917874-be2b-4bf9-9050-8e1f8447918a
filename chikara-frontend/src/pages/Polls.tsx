import SurveyComponent from "@/components/Polls/SurveyComponent";
import SurveyResults from "@/components/Polls/SurveyResults";
import Spinner from "@/components/Spinners/Spinner";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { Disclosure } from "@headlessui/react";
import { format } from "date-fns";
import { ChevronUp } from "lucide-react";
import { useGetAvailablePolls } from "@/features/suggestions/api/usePolls";

interface Poll {
    id: number;
    title: string;
    endsAt?: string;
}

interface PollResultsProps {
    availablePolls: Poll[];
}

interface AvailablePollsProps {
    availablePolls: Poll[];
}

const Polls = () => {
    const { data: currentUser } = useFetchCurrentUser();
    const { data: currentPolls, isLoading } = useGetAvailablePolls();

    if (isLoading) return <Spinner center />;
    const availablePolls = currentUser?.userType === "admin" ? [] : currentPolls;
    return (
        <div className="pt-1 md:p-4">
            <div className="mx-2 max-w-6xl rounded-lg border border-gray-600 bg-gray-800 px-3 py-4 md:mx-auto">
                {currentPolls?.length > 0 && (
                    <div className="mb-8">
                        <p className="text-center text-2xl text-green-500">Available Polls</p>
                        <AvailablePolls availablePolls={currentPolls} />
                    </div>
                )}
                <PollResults availablePolls={availablePolls} />
            </div>
        </div>
    );
};

export default Polls;

const PollResults = ({ availablePolls }: PollResultsProps) => {
    return (
        <div className="flex flex-col">
            <p className="text-center text-2xl text-blue-500">Poll Results</p>
            <SurveyResults availablePolls={availablePolls} />
        </div>
    );
};

const AvailablePolls = ({ availablePolls }: AvailablePollsProps) => {
    return (
        <>
            {availablePolls.map((poll, index) => (
                <Disclosure key={poll.id} defaultOpen={false} as="div" className="my-2">
                    {({ open }) => (
                        <>
                            <Disclosure.Button
                                className={cn(
                                    open ? "rounded-t-lg" : "rounded-lg",
                                    index === 0 ? "bg-blue-600" : "bg-indigo-600",
                                    "focus-visible:ring/10 flex w-full justify-between px-4 py-3 text-left font-medium text-lg focus:outline-hidden dark:text-slate-200 dark:text-stroke-sm"
                                )}
                            >
                                <div className="mx-3 flex w-full flex-row justify-between gap-0">
                                    <p className="w-fit font-body font-semibold text-stroke-sm">{poll.title}</p>
                                    {/* <p className="text-sm text-custom-yellow ml-auto mr-4 my-auto">
                    Closes {format(new Date(poll?.endsAt), "dd/MM")}
                  </p> */}
                                </div>
                                <ChevronUp className={`${open ? "rotate-180" : ""} my-auto size-6 text-white`} />
                            </Disclosure.Button>
                            <Disclosure.Panel className="rounded-b-lg border-x border-b text-gray-500 text-sm shadow-xl dark:border-slate-600 dark:bg-slate-900 dark:text-slate-300">
                                <SurveyComponent poll={poll} />
                            </Disclosure.Panel>
                        </>
                    )}
                </Disclosure>
            ))}
        </>
    );
};
