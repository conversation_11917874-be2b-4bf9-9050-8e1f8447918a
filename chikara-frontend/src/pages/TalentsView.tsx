import { Link, useLocation } from "react-router-dom";
import TalentTree from "./TalentTree";

import defenceImg from "@/assets/images/UI/Skills/defence.png";
import dexterityImg from "@/assets/images/UI/Skills/dexterity.png";
import intelligenceImg from "@/assets/images/UI/Skills/intelligence.png";
import staminaImg from "@/assets/images/UI/Skills/stamina.png";
import strengthImg from "@/assets/images/UI/Skills/strength.png";
import RespecTalents from "@/features/talents/components/RespecTalents";
import { cn } from "@/lib/utils";

interface SkewedButtonProps {
    text: string;
    href: string;
    bgCol: string;
    image: string;
    disabled?: boolean;
    borderCol: string;
}

export default function TalentsView() {
    const location = useLocation();
    if (location.pathname === "/talents") {
        return (
            <div className="md:mx-auto md:max-w-6xl">
                <RespecTalents />
                <div className="m-5 flex w-full flex-col gap-3 md:gap-4">
                    <SkewedButton
                        text="Strength"
                        href="strength"
                        bgCol=" from-blue-600 to-blue-800 before:from-blue-600 before:to-blue-800"
                        borderCol="before:border-blue-500 border-blue-500"
                        image={strengthImg}
                    />
                    <SkewedButton
                        text="Dexterity"
                        href="dexterity"
                        image={dexterityImg}
                        bgCol=" from-amber-600 to-amber-800 before:from-amber-600 before:to-amber-800"
                        borderCol="before:border-amber-500 border-amber-500"
                    />
                    <SkewedButton
                        text="Defence"
                        href="defence"
                        image={defenceImg}
                        bgCol="from-green-600 to-green-800 before:from-green-600 before:to-green-800"
                        borderCol="before:border-green-500 border-green-500"
                    />
                    <SkewedButton
                        text="Intelligence"
                        href="intelligence"
                        image={intelligenceImg}
                        bgCol="from-indigo-600 to-indigo-800 before:from-indigo-600 before:to-indigo-800"
                        borderCol="before:border-indigo-500 border-indigo-500"
                    />
                    <SkewedButton
                        text="Endurance"
                        href="endurance"
                        image={staminaImg}
                        bgCol="from-sky-600 to-sky-800 before:from-sky-600 before:to-sky-800"
                        borderCol="border-sky-500 before:border-sky-500"
                    />
                    <SkewedButton
                        disabled
                        text="Vitality"
                        href="vitality"
                        image={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/charisma.png`}
                        bgCol="bg-gray-800 before:bg-gray-800"
                        borderCol="border-black before:border-black"
                    />
                </div>
            </div>
        );
    } else {
        return <TalentTree pathname={location.pathname} />;
    }
}

const SkewedButton = ({ text, href, bgCol, image, disabled, borderCol }: SkewedButtonProps) => {
    return (
        <div className="relative">
            <Link to={href}>
                <div
                    className={cn(
                        "font-accent skewedContainer -ml-3 flex 3xl:h-24 h-18 w-[90%] border-y-2 border-l-2 bg-linear-to-t shadow-2xl before:border-y-2 before:border-r-2 before:bg-linear-to-t active:brightness-125 xl:h-[4.1rem]",
                        bgCol,
                        borderCol
                    )}
                >
                    <img
                        className={cn(disabled && "grayscale", "z-10 my-auto ml-2 h-[90%] drop-shadow-xl")}
                        src={image}
                        alt=""
                    />
                    <p
                        className={cn(
                            "z-10 m-auto text-2xl text-stroke-s-sm uppercase",
                            disabled ? "text-gray-600" : "dark:text-slate-200"
                        )}
                    >
                        {text}
                    </p>
                </div>
            </Link>

            <div
                className={cn(
                    "wavyBackgroundAfter right-[-4px] z-10 my-0.5 w-[8%] cursor-default border-l-2 bg-[#111521] md:w-[10%]",
                    borderCol
                )}
            ></div>
            <div className="wavyBackground -ml-2.5 pointer-events-none absolute top-0 left-0 mt-0.5 h-[94%] w-full invert"></div>
        </div>
    );
};
