import { usePersistStore } from "@/app/store/stores";
import arcadeIcon from "@/assets/icons/arcade.png";
import auctionIcon from "@/assets/icons/auction.webp";
import bankIcon from "@/assets/icons/bankicon.webp";
import bossIcon from "@/assets/icons/boss2.webp";
import casinoIcon from "@/assets/icons/casino.webp";
import currentJob from "@/assets/icons/currentJob.webp";
import trainingIcon from "@/assets/icons/gymicon.webp";
import hospitalIcon from "@/assets/icons/hospitalicon.webp";
import housingIcon from "@/assets/icons/housing.webp";
import jailIcon from "@/assets/icons/jailicon.webp";
import jobsIcon from "@/assets/icons/jobs.webp";
import premiumIcon from "@/assets/icons/premium.webp";
import referIcon from "@/assets/icons/refer.webp";
import shopsIcon from "@/assets/icons/shopsicon.webp";
import shrineIcon from "@/assets/icons/shrineicon.webp";
import springsIcon from "@/assets/icons/springsicon.webp";
import arrow from "@/assets/images/UI/arrow.gif";
import { levelGates } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useIsQuestInProgress from "@/hooks/api/useIsQuestInProgress";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { ChevronRight, Hammer, List, Map } from "lucide-react";
import { Fragment } from "react";
import { Link } from "react-router-dom";

interface Page {
    name: string;
    icon: string;
    link: string;
    construction?: boolean;
    disabled?: boolean;
    levelGate?: string;
}

export default function Explore() {
    const { data: currentUser } = useFetchCurrentUser();
    const isMobile = useCheckMobileScreen();
    const { explorePageSetting, setExplorePageSetting } = usePersistStore();

    const { SHRINE_DISABLED } = useGameConfig();

    const isPageDisabled = (page: Page): string | boolean => {
        const levelGate = levelGates(page?.levelGate, 0, currentUser?.level);
        if (levelGate) return levelGate;
        return false;
    };

    const pages: Page[] = [
        { name: "Training", icon: trainingIcon, link: "/training", construction: false },
        { name: "Shops", icon: shopsIcon, link: "/shops" },
        { name: "Pets", icon: premiumIcon, link: "/pets" },
        { name: "Bank", icon: bankIcon, link: "/bank" },
        {
            name: "Part-Time Job",
            icon: currentUser?.jobId > 0 ? currentJob : jobsIcon,
            link: currentUser?.jobId > 0 ? "/job" : "/joblistings",
            levelGate: "job",
        },
        {
            name: "Market",
            icon: auctionIcon,
            link: "/market",
            levelGate: "market",
        },
        {
            name: "Shrine",
            icon: shrineIcon,
            link: "/shrine",
            disabled: SHRINE_DISABLED,
        },
        { name: "Hospital", icon: hospitalIcon, link: "/hospital" },
        { name: "Jail", icon: jailIcon, link: "/jail" },
        { name: "Casino", icon: casinoIcon, link: "/casino", construction: false },
        { name: "Arcade", icon: arcadeIcon, link: "/arcade", construction: false, levelGate: "arcade" },

        {
            name: "Housing",
            icon: housingIcon,
            link: "/housing",
            construction: true,
        },

        {
            name: "World Boss",
            icon: bossIcon,
            link: "/worldboss",
            construction: true,
        },

        {
            name: "Hot Springs",
            icon: springsIcon,
            link: "/springs",
            construction: true,
        },

        // {
        //   name: "Premium",
        //   icon: premiumIcon,
        //   link: "/premium",
        // },
    ];

    const ListView = () => {
        // const isFirstQuestInProgress = useIsQuestInProgress("Training Day");
        const isFirstQuestInProgress = false;

        return (
            <ul className="divide-y divide-gray-200 bg-white text-left shadow-sm dark:divide-gray-700 dark:bg-gray-800 ">
                {pages.map((page) => (
                    <Fragment key={page.link}>
                        {page.construction || page.disabled ? null : (
                            <li key={page.link}>
                                <Link
                                    to={page.link}
                                    className="relative block hover:bg-gray-50 dark:hover:bg-gray-900"
                                    style={{
                                        pointerEvents: isPageDisabled(page) && "none",
                                    }}
                                >
                                    {page.name === "Training" && isFirstQuestInProgress && (
                                        <img
                                            className="-rotate-90 -translate-x-1/2 -top-16 absolute left-32 z-50 size-24 scale-75"
                                            src={arrow}
                                            alt=""
                                        />
                                    )}
                                    <div className="flex items-center px-1 py-3 sm:px-6 md:py-1">
                                        <div className="flex min-w-0 flex-1 items-center">
                                            <div className="shrink-0">
                                                <div className="relative dark:brightness-90">
                                                    <img
                                                        src={page.icon}
                                                        alt=""
                                                        className={`hidden size-[50px] md:block ${
                                                            page.construction && "brightness-50"
                                                        }`}
                                                    />
                                                    <img
                                                        src={page.icon}
                                                        alt=""
                                                        className={`size-[35px] md:hidden ${
                                                            page.construction && "brightness-50"
                                                        }`}
                                                    />
                                                    {page.construction && (
                                                        <Hammer className="absolute top-2 left-0 z-40 text-yellow-500" />
                                                    )}
                                                </div>
                                            </div>
                                            <div className="min-w-0 flex-1 px-4 font-lili text-indigo-600 uppercase md:grid md:grid-cols-2 md:gap-4 dark:text-slate-100 dark:text-stroke-s-sm">
                                                <div>
                                                    {isPageDisabled(page) ? (
                                                        <p className="truncate text-gray-600 text-sm md:text-lg dark:text-gray-400">
                                                            {isPageDisabled(page)}
                                                        </p>
                                                    ) : (
                                                        <p
                                                            className={cn(
                                                                "truncate text-sm md:text-lg",
                                                                page.construction &&
                                                                    "text-indigo-900 dark:text-gray-600"
                                                            )}
                                                        >
                                                            {page.name}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <ChevronRight className="size-5 text-gray-400" aria-hidden="true" />
                                        </div>
                                    </div>
                                </Link>
                            </li>
                        )}
                    </Fragment>
                ))}
            </ul>
        );
    };

    return (
        <div
            className={cn(
                "mx-auto overflow-hidden border bg-gray-300 text-right shadow-sm md:mb-0 md:rounded-md dark:border-gray-600 dark:bg-gray-700",
                explorePageSetting === "list" && "max-w-6xl"
            )}
        >
            <span className="relative z-0 my-1 mr-2 hidden rounded-md border border-gray-500 shadow-xs md:inline-flex">
                <button
                    disabled={!!isMobile}
                    type="button"
                    className={`${
                        explorePageSetting === "map" || isMobile
                            ? "bg-slate-200 md:bg-gray-400"
                            : "bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-200"
                    } relative inline-flex items-center rounded-l-md border border-gray-200 p-2 font-medium text-sm focus:z-10 focus:border-gray-500 focus:outline-hidden focus:ring-1 focus:ring-gray-500`}
                    onClick={() => setExplorePageSetting("map")}
                >
                    <span className="sr-only">Map</span>
                    <Map className={`size-5 ${isMobile ? "text-gray-400" : "text-gray-700"}`} aria-hidden="true" />
                </button>
                <button
                    disabled={!!isMobile}
                    type="button"
                    color="red"
                    className={`${
                        explorePageSetting === "list" || isMobile
                            ? "bg-slate-200 md:bg-gray-400"
                            : "bg-white text-gray-500 hover:bg-gray-100 dark:bg-gray-200"
                    } -ml-px relative inline-flex items-center rounded-r-md border border-gray-200 bg-white p-2 font-medium text-gray-500 text-sm focus:z-10 focus:border-gray-500 focus:outline-hidden focus:ring-1 focus:ring-gray-500`}
                    onClick={() => setExplorePageSetting("list")}
                >
                    <span className="sr-only">List</span>
                    <List className={`size-5 ${isMobile ? "text-gray-400" : "text-gray-700"}`} aria-hidden="true" />
                </button>
            </span>
            <ListView />
        </div>
    );
}
