export default function ShoeLocker() {
    return (
        <div className="mt-5 flex flex-col">
            <p className="mx-auto px-4 text-lg">
                The Shoe Lockers are a place where students can change from outside footwear to school slippers. They
                are a popular place to confess your feelings for someone
            </p>
            <p className="mx-auto text-sm">Change this text ^</p>
            <div className="mx-auto mt-10">
                <p>Leave a confession</p>
            </div>

            <div className="mx-auto mt-1 flex w-2/5 flex-col">
                <div className="my-3">
                    <label htmlFor="studentid" className="block font-medium text-gray-700 text-sm">
                        Student ID
                    </label>
                    <div className="mt-1 flex rounded-md shadow-xs">
                        <span className="inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                            #
                        </span>
                        <input
                            type="text"
                            name="studentid"
                            id="studentid"
                            className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="1"
                        />
                    </div>
                </div>
                <div>
                    <label htmlFor="message" className="mb-3 block">
                        Message
                    </label>
                    <textarea
                        id="message"
                        rows="4"
                        className="block size-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-gray-900 text-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Leave confession message here..."
                    ></textarea>
                </div>

                <button className="sidebar_button mx-auto my-3 w-28 rounded-md px-3 py-2 font-semibold focus:outline-hidden">
                    Send
                </button>
            </div>
        </div>
    );
}
