// import { useState } from "react";
// import { BarChart, Bar, XAxis, YA<PERSON><PERSON>, Tooltip, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";
// import {
//     FaRunning,
//     FaUsers,
//     FaYenSign,
//     FaBox,
//     FaBolt,
//     FaComments,
//     FaFrown,
//     FaSkullCrossbones,
//     FaSkull,
//     FaCrosshairs,
//     FaMoneyBill,
//     FaCheckSquare,
//     FaBook,
//     FaPlusCircle,
//     FaHeartBroken,
//     FaClock,
//     FaHeart,
//     FaChevronUp,
//     FaDice,
//     FaDumbbell,
//     FaRedo,
//     FaTrophy,
//     FaUserNinja,
//     FaRobot,
//     FaMoneyBillWave,
//     FaUserSecret,
//     FaHam<PERSON>,
//     FaShoppingCart,
//     FaGavel,
//     FaTicketAlt,
//     FaUniversity,
//     FaBriefcase,
//     FaBookOpen,
//     FaPencilAlt,
//     FaMapMarkedAlt,
//     FaUserAstronaut,
//     FaAppleAlt,
//     FaSearchLocation,
//     FaBell,
//     FaEnvelope,
//     FaUserCircle,
//     FaLightbulb,
//     FaThumbsUp,
//     FaThumbsDown,
//     FaPoll,
// } from "react-icons/fa";
// import { MdMessage, MdLeaderboard } from "react-icons/md";
// import { IoCheckboxOutline } from "react-icons/io5";
// import { ImCross } from "react-icons/im";
// import { GiLockedChest } from "react-icons/gi";
// import { cn } from "@/lib/utils";
// import Tabs from "@/components/Tabs";
// import { PiExamFill } from "react-icons/pi";
// import { BsChatLeftTextFill } from "react-icons/bs";
// import { RiShoppingBasketFill } from "react-icons/ri";
// import globalStats from "../constants/globalStats";
// import { getEmote } from "../features/chat/helpers/chatHelpers";

// const formatNumber = (num) => num.toLocaleString();

// const StatSource = ({ currentSource, setCurrentSource }) => {
//     const options = [
//         { name: "Global Statistics", value: "global", current: currentSource === "global" },
//         // {
//         //   name: "My Statistics",
//         //   value: "personal",
//         //   current: currentSource === "personal",
//         //   disabled: true,
//         // },
//     ];
//     return (
//         <div className="flex w-full divide-x-2 divide-white/25 rounded-t-lg border-indigo-500 border-x border-t">
//             {options.map((tab, tabIdx) => (
//                 <button
//                     key={tab.name}
//                     aria-current={tab.current ? "page" : undefined}
//                     className={cn(
//                         tab.current ? "text-white" : "text-gray-300",
//                         tabIdx === 0 ? "rounded-tl-lg" : "rounded-tr-lg",
//                         tabIdx === options.length - 1 ? "" : "",
//                         tab.disabled ? "grayscale" : "",
//                         "group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-indigo-900",
//                     )}
//                     onClick={() => (tab.disabled ? null : setCurrentSource(tab.value))}
//                 >
//                     <span>{tab.name}</span>
//                     <span
//                         aria-hidden="true"
//                         className={cn(
//                             // tab.current ? "bg-custom-yellow" : "bg-indigo-700",
//                             tab.current ? "bg-custom-yellow" : "bg-indigo-700 grayscale",
//                             "absolute inset-x-0 bottom-0 h-[0.2rem]",
//                         )}
//                     />
//                 </button>
//             ))}
//         </div>
//     );
// };

// const colorVariations = [
//     "before:bg-blue-500",
//     "before:bg-emerald-500",
//     "before:bg-amber-500",
//     "before:bg-rose-500",
//     "before:bg-violet-500",
//     "before:bg-indigo-500",
// ];

// const iconColorVariations = [
//     "text-blue-500",
//     "text-emerald-500",
//     "text-amber-500",
//     "text-rose-500",
//     "text-violet-500",
//     "text-indigo-500",
// ];

// const StatCard = ({ title, value, icon: Icon, className, colorIndex = 0 }) => (
//     <div
//         className={cn(
//             "statBoxBig text-stroke-md! flex flex-col items-center gap-2 px-4 py-5",
//             colorVariations[colorIndex],
//             className,
//         )}
//     >
//         <Icon className={cn(iconColorVariations[colorIndex], "mr-4")} size={32} />
//         <div>
//             <h3 className="font-medium text-gray-200 text-lg">{title}</h3>
//             <p className="font-medium text-2xl text-custom-yellow">{value}</p>
//         </div>
//     </div>
// );

// const BarChartComponent = ({ data, dataKey, nameKey, fill = "#3b82f6" }) => (
//     <ResponsiveContainer className="text-blue-500" width="100%" height={200}>
//         <BarChart data={data}>
//             <XAxis stroke="#fff" dataKey={nameKey} />
//             <YAxis stroke="#fff" />
//             <Tooltip contentStyle={{ backgroundColor: "#1f2937", border: "none" }} itemStyle={{ color: "#fff" }} />
//             <Bar dataKey={dataKey} fill={fill} />
//         </BarChart>
//     </ResponsiveContainer>
// );

// const GameStats = () => {
//     const [currentSource, setCurrentSource] = useState("global");
//     const [statTab, setStatTab] = useState("players");

//     const { playersStats, combatStats, economyStats, activitiesStats, socialStats, emoteStats } = globalStats;

//     const statsTypeTabs = [
//         { value: "players", label: "Players", icon: <FaUsers className="h-6 w-auto lg:h-5" /> },
//         {
//             value: "combat",
//             label: "Combat",
//             icon: <MdLeaderboard className="h-6 w-auto lg:h-5" />,
//         },
//         {
//             value: "economy",
//             label: "Economy",
//             icon: <RiShoppingBasketFill className="h-6 w-auto lg:h-5" />,
//         },
//         {
//             value: "activities",
//             label: "Activities",
//             icon: <MdLeaderboard className="h-6 w-auto lg:h-5" />,
//         },
//         {
//             value: "social",
//             label: "Social",
//             icon: <BsChatLeftTextFill className="h-6 w-auto lg:h-5" />,
//         },
//     ];

//     return (
//         <>
//             <p className="mx-auto mb-3 w-fit rounded-lg border border-indigo-600/50 bg-black/50 px-12 py-1 text-center text-3xl text-sky-300">
//                 April 20th - July 12th 2024
//             </p>
//             <div className="cardBorder1 mx-auto max-w-7xl rounded-lg ">
//                 <StatSource currentSource={currentSource} setCurrentSource={setCurrentSource} />
//                 <div className="mt-4">
//                     {" "}
//                     <Tabs tabs={statsTypeTabs} selectedTab={statTab} setSelectedTab={setStatTab} />
//                 </div>
//                 <div className="rounded-b-lg border-gray-600/50 border-t bg-[#14191E] p-4 lg:p-8 lg:pt-4">
//                     {statTab === "players" && <GlobalPlayerStats playersStats={playersStats} />}
//                     {statTab === "combat" && <GlobalCombatStats combatStats={combatStats} />}
//                     {statTab === "economy" && <GlobalEconomyStats economyStats={economyStats} />}
//                     {statTab === "activities" && <GlobalActivitiesStats activitiesStats={activitiesStats} />}
//                     {statTab === "social" && <GlobalSocialStats socialStats={socialStats} emoteStats={emoteStats} />}
//                 </div>
//             </div>
//         </>
//     );
// };

// const GlobalPlayerStats = ({ playersStats }) => {
//     return (
//         <div className="space-y-10">
//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-3">
//                 {[
//                     {
//                         title: "Total Registered Players",
//                         value: formatNumber(playersStats.totalUsers),
//                         icon: FaUsers,
//                     },
//                     {
//                         title: "Total Actions",
//                         value: formatNumber(playersStats.totalActions),
//                         icon: FaRunning,
//                     },
//                     {
//                         title: "Training Button Clicks",
//                         value: formatNumber(playersStats.trainingButtonClicks),
//                         icon: FaDumbbell,
//                     },
//                     {
//                         title: "Unlocked Talents",
//                         value: formatNumber(playersStats.userTalents),
//                         icon: FaBook,
//                     },
//                     {
//                         title: "Equipped Skills",
//                         value: formatNumber(playersStats.equippedSkills),
//                         icon: FaBolt,
//                     },
//                     {
//                         title: "Talent Respecs",
//                         value: formatNumber(playersStats.totalTalentRespecs),
//                         icon: FaRedo,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>
//             <TalentStats />
//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-3">
//                 {[
//                     { title: "Consumables Used", value: playersStats.consumablesUsed, icon: FaAppleAlt },
//                     { title: "Exams Completed", value: playersStats.examsCompleted, icon: PiExamFill },
//                     { title: "Players Jailed", value: playersStats.jailedTotal, icon: FaFrown },
//                     {
//                         title: "Players Hospitalised",
//                         value: playersStats.hospitalisedTotal,
//                         icon: FaPlusCircle,
//                     },
//                     {
//                         title: "Total Players Injured",
//                         value: formatNumber(playersStats.totalInjuries),
//                         icon: FaHeartBroken,
//                     },
//                     {
//                         title: "Player Revivals/Heals",
//                         value: formatNumber(playersStats.totalRevives),
//                         icon: FaHeart,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>
//         </div>
//     );
// };

// const CustomTick = ({ x, y, payload }) => {
//     const emoteURL = getEmote(payload.value);

//     return <image href={emoteURL} x={x - 10} y={y + 10} height={20} width={20} alt="emote" />;
// };

// const EmoteBarChart = ({ emoteStats }) => (
//     <div className="barChartBG col-span-2 rounded-lg p-4">
//         <h2 className="mb-4 font-medium text-white text-xl">Top Chat Emotes</h2>
//         <ResponsiveContainer className="-ml-8 md:-ml-5 md:w-full! w-[110%]!" height={300}>
//             <BarChart data={emoteStats}>
//                 <XAxis dataKey="name" stroke="#fff" tick={<CustomTick />} interval={0} />
//                 <YAxis stroke="#fff" />
//                 <Tooltip
//                     contentStyle={{ backgroundColor: "#1f2937", border: "none" }}
//                     labelStyle={{ color: "#fff" }}
//                     itemStyle={{ color: "#fff" }}
//                     labelFormatter={(value) => {
//                         return (
//                             <div className="flex items-center gap-2 text-blue-500">
//                                 :{value} <img className="h-8 w-auto" src={getEmote(value)} alt={value} />
//                             </div>
//                         );
//                     }}
//                 />
//                 <Bar dataKey="value" fill="#3b82f6" />
//             </BarChart>
//         </ResponsiveContainer>
//     </div>
// );

// const GlobalSocialStats = ({ socialStats, emoteStats }) => {
//     emoteStats?.sort((a, b) => b.value - a.value);
//     return (
//         <div className="space-y-8">
//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     { title: "Chat Messages", value: socialStats.chatMessages, icon: FaComments },
//                     { title: "Private Messages", value: socialStats.privateMessages, icon: FaEnvelope },
//                     { title: "Profile Comments", value: socialStats.profileComments, icon: FaUserCircle },
//                     { title: "Gang Members", value: socialStats.gangMembers, icon: FaUsers },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>

//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     {
//                         title: "Total Suggestions",
//                         value: socialStats.totalSuggestions,
//                         icon: FaLightbulb,
//                     },
//                     {
//                         title: "Completed Suggestions",
//                         value: socialStats.completedSuggestions,
//                         icon: FaCheckSquare,
//                     },
//                     {
//                         title: "Accepted Suggestions",
//                         value: socialStats.acceptedSuggestions,
//                         icon: IoCheckboxOutline,
//                     },
//                     {
//                         title: "Denied Suggestions",
//                         value: socialStats.deniedSuggestions,
//                         icon: ImCross,
//                     },
//                     {
//                         title: "Suggestion Comments",
//                         value: socialStats.suggestionComments,
//                         icon: MdMessage,
//                     },
//                     { title: "Suggestion Upvotes", value: socialStats.suggestionUpvotes, icon: FaThumbsUp },
//                     {
//                         title: "Suggestion Downvotes",
//                         value: socialStats.suggestionDownvotes,
//                         icon: FaThumbsDown,
//                     },
//                     { title: "Poll Responses", value: socialStats.pollResponses, icon: FaPoll },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>
//             <EmoteBarChart emoteStats={emoteStats} />
//         </div>
//     );
// };

// const GlobalCombatStats = ({ combatStats }) => {
//     const combatStatValues = [
//         { name: "STR", value: combatStats.totalStrength },
//         { name: "INT", value: combatStats.totalIntelligence },
//         { name: "DEX", value: combatStats.totalDexterity },
//         { name: "DEF", value: combatStats.totalDefence },
//         { name: "STA", value: combatStats.totalStamina },
//     ];

//     return (
//         <div className="space-y-8">
//             <div className="barChartBG col-span-2 rounded-lg p-4">
//                 <h2 className="mb-4 font-medium text-white text-xl">Total Stat Distribution</h2>
//                 <ResponsiveContainer width="100%" height={300}>
//                     <BarChart data={combatStatValues}>
//                         <XAxis dataKey="name" stroke="#fff" />
//                         <YAxis stroke="#fff" />
//                         <Tooltip
//                             contentStyle={{ backgroundColor: "#1f2937", border: "none" }}
//                             labelStyle={{ color: "#fff" }}
//                         />
//                         <Bar dataKey="value" fill="#3b82f6" />
//                     </BarChart>
//                 </ResponsiveContainer>
//             </div>
//             <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
//                 <div className="flex flex-col gap-2">
//                     <h2 className="font-medium text-2xl text-white">PvP Combat</h2>
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//                         {[
//                             {
//                                 title: "PvP Wins",
//                                 value: formatNumber(combatStats.pvpWins),
//                                 icon: FaCrosshairs,
//                                 className: "col-span-2",
//                             },
//                             {
//                                 title: "Players Mugged",
//                                 value: formatNumber(combatStats.playersMugged),
//                                 icon: FaUserNinja,
//                             },
//                             {
//                                 title: "Total Mugged",
//                                 value: `¥${formatNumber(combatStats.totalMugged)}`,
//                                 icon: FaMoneyBillWave,
//                             },
//                             {
//                                 title: "Bounties Claimed",
//                                 value: formatNumber(combatStats.bountiesClaimed),
//                                 icon: FaUserSecret,
//                             },
//                             {
//                                 title: "Bounty Profit",
//                                 value: `¥${formatNumber(combatStats.bountyAmountClaimed)}`,
//                                 icon: FaMoneyBillWave,
//                             },
//                         ].map((stat, index) => (
//                             <StatCard
//                                 key={stat.title}
//                                 title={stat.title}
//                                 value={stat.value}
//                                 icon={stat.icon}
//                                 className={stat.className}
//                                 colorIndex={index % colorVariations.length}
//                             />
//                         ))}
//                     </div>
//                 </div>

//                 <div className="flex flex-col gap-2">
//                     <h2 className="font-medium text-2xl text-white">NPC Combat</h2>
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//                         {[
//                             {
//                                 title: "NPCs Defeated",
//                                 value: formatNumber(combatStats.npcsDefeated),
//                                 icon: FaSkull,
//                                 className: "col-span-2",
//                             },
//                             {
//                                 title: "Streets Bosses Defeated",
//                                 value: formatNumber(combatStats.bossesDefeated),
//                                 icon: FaSkullCrossbones,
//                             },
//                             {
//                                 title: "Rooftop Bosses Defeated",
//                                 value: formatNumber(combatStats.rooftopBossesDefeated),
//                                 icon: FaSkullCrossbones,
//                             },
//                             {
//                                 title: "Players Mugged by NPCs",
//                                 value: formatNumber(combatStats.playersMuggedByNPCs),
//                                 icon: FaMoneyBill,
//                             },
//                             {
//                                 title: "NPC Wins",
//                                 value: formatNumber(combatStats.playersMuggedByNPCs),
//                                 icon: FaRobot,
//                             },
//                         ].map((stat, index) => (
//                             <StatCard
//                                 key={stat.title}
//                                 title={stat.title}
//                                 value={stat.value}
//                                 icon={stat.icon}
//                                 className={stat.className}
//                                 colorIndex={index % colorVariations.length}
//                             />
//                         ))}
//                     </div>
//                 </div>
//             </div>
//         </div>
//     );
// };

// const GlobalEconomyStats = ({ economyStats }) => {
//     const popularJobs = [
//         { name: "Nemar Ramen", value: 22 },
//         { name: "Robo-San Construction", value: 1 },
//         { name: "Costar Cafe", value: 2 },
//         { name: "PGS Corp", value: 15 },
//         { name: "5-11 Convenience Store", value: 2 },
//         { name: "Envydia Computers", value: 10 },
//     ];

//     const COLORS = ["#FF8042", "#40e0d0", "#0dadf2", "#f205c3", "#3d8c2a", "#c72041"];

//     return (
//         <div className="space-y-8">
//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     {
//                         title: "Circulating Yen",
//                         value: `¥${formatNumber(economyStats.circulatingYen)}`,
//                         icon: FaYenSign,
//                     },
//                     {
//                         title: "Inventory Items",
//                         value: formatNumber(economyStats.inventoryItems),
//                         icon: FaBox,
//                     },
//                     {
//                         title: "Items Crafted",
//                         value: formatNumber(economyStats.itemsCrafted),
//                         icon: FaHammer,
//                     },
//                     { title: "Item Drops", value: formatNumber(economyStats.itemDrops), icon: GiLockedChest },
//                     {
//                         title: "Shop Purchases",
//                         value: formatNumber(economyStats.totalShopPurchases),
//                         icon: FaShoppingCart,
//                     },
//                     {
//                         title: "Auctions Sold",
//                         value: formatNumber(economyStats.totalAuctionsSold),
//                         icon: FaGavel,
//                     },
//                     {
//                         title: "Casino Net Profit",
//                         value: `¥${formatNumber(economyStats.casinoNetProfit)}`,
//                         icon: FaDice,
//                     },
//                     {
//                         title: "Bank Transactions",
//                         value: formatNumber(economyStats.bankTransactions),
//                         icon: FaUniversity,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>

//             <div className="col-span-1">
//                 <div className="barChartBG rounded-lg p-4">
//                     <h2 className="mb-4 font-medium text-white text-xl">Job Distribution</h2>
//                     <ResponsiveContainer width="100%" height={300}>
//                         <PieChart className="xl:scale-125!">
//                             <Pie
//                                 data={popularJobs}
//                                 cx="50%"
//                                 cy="50%"
//                                 labelLine={false}
//                                 outerRadius={80}
//                                 fill="#8884d8"
//                                 dataKey="value"
//                                 label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
//                             >
//                                 {popularJobs.map((entry, index) => (
//                                     <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
//                                 ))}
//                             </Pie>
//                             <Tooltip
//                                 contentStyle={{ backgroundColor: "#1f2937", border: "none" }}
//                                 labelStyle={{ color: "#fff" }}
//                                 itemStyle={{ color: "#9ca3af" }}
//                             />
//                         </PieChart>
//                     </ResponsiveContainer>
//                 </div>
//             </div>

//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     { title: "Employed Players", value: economyStats.employedPlayers, icon: FaBriefcase },
//                     { title: "Job Payouts", value: formatNumber(economyStats.jobPayouts), icon: FaYenSign },
//                     {
//                         title: "Recipes Unlocked",
//                         value: formatNumber(economyStats.craftingRecipesUnlocked),
//                         icon: FaPencilAlt,
//                     },
//                     {
//                         title: "Lottery Winners",
//                         value: formatNumber(economyStats.lotteryWinners),
//                         icon: FaTicketAlt,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>
//         </div>
//     );
// };

// const TalentStats = () => {
//     const popularTalents = [
//         { talentId: 1, name: "Brute Force", count: 42 },
//         { talentId: 10, name: "Rigidity", count: 41 },
//         { talentId: 22, name: "Multitasker", count: 26 },
//         { talentId: 21, name: "Speed Crafter", count: 25 },
//         { talentId: 33, name: "Free Movement", count: 24 },
//     ];

//     const popularSkills = [
//         { talentId: 6, name: "Rage", count: 27 },
//         { talentId: 17, name: "Shield Bash", count: 20 },
//         { talentId: 9, name: "Headbutt", count: 13 },
//         { talentId: 47, name: "Spray", count: 10 },
//         { talentId: 25, name: "Buffer", count: 9 },
//     ];

//     return (
//         <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2">
//             <div className="barChartBG p-4">
//                 <h2 className="mb-4 font-medium text-xl">Most Popular Passive Talents</h2>
//                 <BarChartComponent data={popularTalents} dataKey="count" nameKey="name" fill="#1d5dbb" />
//             </div>
//             <div className="barChartBG p-4 shadow-sm">
//                 <h2 className="mb-4 font-medium text-xl">Most Popular Skills</h2>
//                 <BarChartComponent data={popularSkills} dataKey="count" nameKey="name" fill="#f0b944" />
//             </div>
//         </div>
//     );
// };

// const GlobalActivitiesStats = ({ activitiesStats }) => {
//     return (
//         <div className="space-y-8">
//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     {
//                         title: "Tasks Completed",
//                         value: `${((activitiesStats.tasksCompleted / (activitiesStats.tasksCompleted + activitiesStats.tasksStarted)) * 100).toFixed(0)}%`,
//                         icon: FaTrophy,
//                     },
//                     {
//                         title: "Daily Tasks Completed",
//                         value: activitiesStats.dailyQuestsCompleted,
//                         icon: FaTrophy,
//                     },
//                     { title: "Courses Completed", value: activitiesStats.coursesCompleted, icon: FaBookOpen },
//                     {
//                         title: "Missions Completed",
//                         value: activitiesStats.missionsCompleted,
//                         icon: FaCheckSquare,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>

//             <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
//                 {[
//                     { title: "Event Notifications", value: activitiesStats.eventNotifications, icon: FaBell },
//                     { title: "Mission Hours", value: activitiesStats.totalMissionHours, icon: FaClock },
//                     {
//                         title: "Roguelike Nodes Completed",
//                         value: activitiesStats.roguelikeNodesCompleted,
//                         icon: FaMapMarkedAlt,
//                     },
//                     {
//                         title: "Roguelike Maps Completed",
//                         value: activitiesStats.roguelikeMapsCompleted,
//                         icon: FaMapMarkedAlt,
//                     },
//                     {
//                         title: "Shrines Activated",
//                         value: `${((activitiesStats.totalShrinesActivated / (activitiesStats.totalShrinesActivated + activitiesStats.totalShrinesNotActivated)) * 100).toFixed(0)}%`,
//                         icon: FaCheckSquare,
//                     },
//                     {
//                         title: "Character Encounters",
//                         value: activitiesStats.characterEncounters,
//                         icon: FaUserAstronaut,
//                     },
//                     {
//                         title: "Scavenging Drops",
//                         value: activitiesStats.scavengingItemsDropped,
//                         icon: FaSearchLocation,
//                     },
//                     {
//                         title: "Streets Buffs Collected",
//                         value: activitiesStats.roguelikeBuffsCollected,
//                         icon: FaChevronUp,
//                     },
//                 ].map((stat, index) => (
//                     <StatCard
//                         key={stat.title}
//                         title={stat.title}
//                         value={stat.value}
//                         icon={stat.icon}
//                         colorIndex={index % colorVariations.length}
//                     />
//                 ))}
//             </div>
//         </div>
//     );
// };

// export default GameStats;
