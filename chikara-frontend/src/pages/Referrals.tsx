import Spinner from "@/components/Spinners/Spinner";
import { But<PERSON> } from "@/components/ui/button";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { clsx } from "@/lib/utils";
import { Info } from "lucide-react";
import toast from "react-hot-toast";
import { useGetReferralCodes } from "@/features/auth/api/useGetReferralCodes";

interface ReferralCode {
    code: string;
    claimerId?: number | null;
}

interface CodeInputBoxProps {
    code: ReferralCode;
}

export default function Referrals() {
    const { data: codes, isLoading } = useGetReferralCodes();

    return (
        <>
            <section className="mx-auto rounded-lg bg-gray-100 py-6 md:max-w-3xl dark:bg-gray-800">
                <div className="container mx-auto px-0 md:px-6">
                    <div className="mx-auto max-w-xl text-center">
                        <div className="mb-4">
                            <h1 className="font-medium text-3xl text-custom-yellow tracking-tighter sm:text-4xl md:text-3xl">
                                Refer Your Friends
                            </h1>
                            <p className=" text-gray-500 md:text-lg dark:text-gray-200">
                                Earn awesome rewards by inviting your friends to join the game.
                            </p>
                        </div>

                        <div className="-mx-5 rounded-lg border border-gray-600 bg-white px-4 py-5 text-left shadow-xl md:mx-0 md:p-6 dark:bg-gray-900">
                            {isLoading ? (
                                <Spinner center />
                            ) : (
                                <>
                                    <h2 className="font-medium text-blue-500 text-lg text-stroke-s-sm">
                                        Your Referral Links
                                    </h2>
                                    <CodeInputBox code={codes[0]} />
                                    <CodeInputBox code={codes[1]} />
                                    <CodeInputBox code={codes[2]} />

                                    <p className="-mb-2.5 mt-2.5 flex w-full justify-center gap-2 text-center text-amber-500 text-sm">
                                        <span className="-ml-3 my-auto text-amber-400">
                                            {" "}
                                            <Info />
                                        </span>
                                        Temporarily limited to 3 invites per person.
                                    </p>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="mt-4 w-full px-4 md:container md:px-6">
                    <div className="mx-auto w-full space-y-6 md:max-w-3xl">
                        <div>
                            <h2 className="font-medium text-2xl text-custom-yellow tracking-tighter sm:text-2xl">
                                Rewards
                            </h2>
                            <p className="mt-0 text-gray-500 text-sm md:text-lg dark:text-gray-300">
                                Earn these awesome rewards by referring your friends.
                            </p>
                        </div>
                        <div className="mx-auto grid gap-6 px-14 sm:grid-cols-2 md:grid-cols-3 md:px-12">
                            <div className="rounded-lg border border-gray-600 shadow-xl bg-indigo-950">
                                <div className="flex h-full flex-col items-center justify-center gap-2 p-6">
                                    <GiftIcon className="size-10 text-gray-500 dark:text-gray-400" />
                                    <div className="text-center">
                                        <h3 className="text-green-500 text-lg">Small Gift Box</h3>
                                        <div className="flex flex-col text-custom-yellow text-xs">
                                            <p>4x Stimulant</p>
                                            <p>3x Gourmet Dish</p>
                                        </div>

                                        <p className="mt-2 text-gray-500 text-sm dark:text-blue-400">
                                            When your friend reaches level 10.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="rounded-lg border border-gray-600 shadow-xl bg-indigo-950">
                                <div className="flex flex-col items-center justify-center gap-2 p-6">
                                    <GiftIcon className="size-10 text-gray-500 dark:text-gray-400" />
                                    <div className="text-center">
                                        <h3 className="text-green-500 text-lg">Large Gift Box</h3>
                                        <div className="flex flex-col text-custom-yellow text-xs">
                                            <p>5x Cola</p>
                                            <p>5x Stimulant</p>
                                            <p>1x Bitcoin</p>
                                        </div>

                                        <p className="mt-2 text-gray-500 text-sm dark:text-blue-400">
                                            When your friend reaches level 20.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="rounded-lg border border-gray-600 shadow-xl bg-indigo-950">
                                <div className="flex h-full flex-col items-center justify-center gap-2 p-6">
                                    <TrophyIcon className="size-10 text-gray-500 dark:text-gray-400" />
                                    <div className="text-center">
                                        <h3 className="font-semibold text-green-500 text-lg">???</h3>
                                        <p className="text-gray-500 text-sm dark:text-blue-400">
                                            When your friend reaches level 30.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto space-y-8">
            <div>
              <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl">Your Referrals</h2>
              <p className="mt-4 text-gray-500 dark:text-gray-400 md:text-lg">
                See who's referring the most friends.
              </p>
            </div>
            <div className="rounded-lg bg-white dark:bg-gray-950 shadow-xs">
              <table className="w-full text-left">
                <thead className="bg-gray-100 dark:bg-gray-800">
                  <tr>
                    <th className="px-4 py-3 font-semibold">Rank</th>
                    <th className="px-4 py-3 font-semibold">Player</th>
                    <th className="px-4 py-3 font-semibold">Referrals</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b dark:border-gray-800">
                    <td className="px-4 py-3">1</td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        <DisplayAvatar src="" />
                        <span>John Doe</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 font-semibold">125</td>
                  </tr>
                  <tr className="border-b dark:border-gray-800">
                    <td className="px-4 py-3">2</td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        <DisplayAvatar src="" />
                        <span>Jane Loe</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 font-semibold">98</td>
                  </tr>
                  <tr className="border-b dark:border-gray-800">
                    <td className="px-4 py-3">3</td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        <DisplayAvatar src="" />
                        <span>Tom Moe</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 font-semibold">82</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div> */}
            </section>
        </>
    );
}

interface IconProps {
    className?: string;
}

function GiftIcon(props: IconProps) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <rect x="3" y="8" width="18" height="4" rx="1" />
            <path d="M12 8v13" />
            <path d="M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7" />
            <path d="M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5" />
        </svg>
    );
}

const CodeInputBox = ({ code }: CodeInputBoxProps) => {
    const codeBaseURL = "https://app.battleacademy.io/register?alphaKey=";
    const { data: user } = useGetUserInfo(code.claimerId!, {
        enabled: !!code.claimerId,
    });

    const copyText = (regCode: string) => {
        const urlstring = codeBaseURL + regCode;
        navigator.clipboard.writeText(urlstring);
        toast.success("Copied to clipboard");
        return;
    };

    const getTextValue = (regCode: ReferralCode) => {
        if (regCode.claimerId) {
            return "Claimed by " + user?.username;
        }
        return codeBaseURL + regCode.code;
    };

    return (
        <div className="mt-4 flex items-center text-gray-300">
            <input
                readOnly
                value={getTextValue(code)}
                className={clsx(
                    code.claimerId && "text-center text-green-500",
                    "flex-1 rounded-lg bg-gray-800 text-sm"
                )}
            />
            <Button
                disabled={!!code.claimerId}
                className="hover:border! hover:border-gray-600! ml-4 rounded-lg text-gray-200"
                variant="outline"
                onClick={() => copyText(code.code)}
            >
                Copy
            </Button>
        </div>
    );
};

function SwordIcon(props: IconProps) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <polyline points="14.5 17.5 3 6 3 3 6 3 17.5 14.5" />
            <line x1="13" x2="19" y1="19" y2="13" />
            <line x1="16" x2="20" y1="16" y2="20" />
            <line x1="19" x2="21" y1="21" y2="19" />
        </svg>
    );
}

function TrophyIcon(props: IconProps) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6" />
            <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18" />
            <path d="M4 22h16" />
            <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22" />
            <path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22" />
            <path d="M18 2H6v7a6 6 0 0 0 12 0V2Z" />
        </svg>
    );
}
