import LoadingState from "@/components/LoadingState";
import EventManager from "@/features/notifications/components/EventManager";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Fragment } from "react";
import type { Notification } from "@/features/notifications/components/EventManager";

interface GroupedEvent {
    name: string;
    events: Notification[];
}

export default function Events() {
    const {
        isLoading,
        error,
        data: events,
    } = useQuery(api.notifications.getList.queryOptions({ input: { limit: 5000 } }));

    if (error) return "An error has occurred: " + error.message;

    const groupedObjects = events?.reduce((acc: Record<string, Notification[]>, obj: Notification) => {
        const key = format(new Date(obj.createdAt), "PP");
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    const groupedArray: GroupedEvent[] = [];
    for (const key in groupedObjects) {
        groupedArray.push({ name: key, events: groupedObjects?.[key] });
    }

    return (
        <div className="px-4 text-shadow sm:px-6 md:mx-auto md:max-w-6xl lg:px-8">
            <div className="flex flex-col md:mt-4">
                <div className="-my-2 -mx-4 sm:-mx-6 lg:-mx-8 overflow-x-auto">
                    <LoadingState isAbsolute size={16} isLoading={isLoading}>
                        {groupedArray.length < 1 ? (
                            <p className="mt-12 text-center text-2xl dark:text-gray-200">No events to show!</p>
                        ) : (
                            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8 ">
                                <div className="mb-5 overflow-hidden shadow-sm ring-1 md:rounded-lg dark:ring-gray-600">
                                    <table className="min-w-full">
                                        <tbody className="bg-white dark:bg-gray-800 ">
                                            {groupedArray.map((event, i) => (
                                                <Fragment key={event.name}>
                                                    <tr
                                                        className={cn(
                                                            i !== 0 && "border-t",
                                                            "border-gray-200 dark:border-gray-600"
                                                        )}
                                                    >
                                                        <th
                                                            colSpan={5}
                                                            scope="colgroup"
                                                            className="bg-gray-50 px-4 py-2 text-left font-medium text-sm sm:px-6 dark:bg-gray-900 dark:text-white"
                                                        >
                                                            {event.name}
                                                        </th>
                                                    </tr>
                                                    {event.events.map((singleEvent) => (
                                                        <EventManager key={singleEvent.id} singleEvent={singleEvent} />
                                                    ))}
                                                </Fragment>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </LoadingState>
                </div>
            </div>
        </div>
    );
}
