import Spinner from "@/components/Spinners/Spinner";
import pollsData from "@/constants/polls.json";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { Disclosure } from "@headlessui/react";
import { BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, Title, Tooltip } from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { ChevronUp } from "lucide-react";
import { Bar } from "react-chartjs-2";
import { useSearchParams } from "react-router-dom";
import { useGetPollResults } from "@/features/suggestions/api/usePolls";

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ChartDataLabels);

interface AvailablePoll {
    id: number;
    title: string;
    ended: boolean;
    showResults: boolean;
}

interface PollData {
    id: number;
    title: string;
    pages: Array<{
        name: string;
        elements: Array<{
            name: string;
            title: string;
            choices?: string[];
            type: string;
        }>;
    }>;
}

interface ResultsProps {
    availablePolls: AvailablePoll[];
}

const Results = ({ availablePolls }: ResultsProps) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const id = searchParams.get("id");
    const filteredPolls = pollsData.filter(
        (poll: PollData) => !availablePolls.some((availablePoll) => availablePoll.id === poll.id)
    );

    // If no polls left to display
    if (filteredPolls.length === 0) {
        return <div className="text-center">No polls to display.</div>;
    }

    const getClosingDate = (questionData: PollData) => {
        return null;
        // const endDate = availablePolls?.find(
        //   (poll) => poll?.pollId === selectedTalent?.id,
        // );
        // console.log(questionData);
        // format(new Date(poll.endsAt), "dd/MM")
    };

    return (
        <div>
            {filteredPolls.map((questionData, index) => (
                <Disclosure
                    key={questionData.id}
                    defaultOpen={questionData.id === parseInt(id || "0")}
                    as="div"
                    className="my-2"
                >
                    {({ open }: { open: boolean }) => (
                        <>
                            <Disclosure.Button
                                className={cn(
                                    open ? "rounded-t-lg" : "rounded-lg",
                                    index === 0 ? "bg-blue-600" : "bg-indigo-600",
                                    "focus-visible:ring/10 flex w-full justify-between px-4 py-3 text-left font-medium text-lg focus:outline-hidden dark:text-slate-200 dark:text-stroke-sm"
                                )}
                            >
                                <div className="mx-3 flex w-full flex-row justify-between gap-0">
                                    <p className="w-fit font-body font-semibold text-stroke-sm">{questionData.title}</p>
                                    {/* <p className="text-sm text-custom-yellow ml-auto mr-4 my-auto">
                    Closes {getClosingDate(questionData)}
                  </p> */}
                                </div>
                                <ChevronUp className={`${open ? "rotate-180" : ""} my-auto size-6 text-white`} />
                            </Disclosure.Button>
                            <Disclosure.Panel className="rounded-b-lg border-x border-b p-4 text-gray-500 text-sm shadow-xl dark:border-slate-600 dark:bg-slate-900 dark:text-slate-300">
                                <SurveyGroupContainer key={index} pollId={questionData.id} questions={questionData} />
                            </Disclosure.Panel>
                        </>
                    )}
                </Disclosure>
            ))}
        </div>
    );
};

interface SurveyGroupContainerProps {
    pollId: number;
    questions: PollData;
}

interface PollResult {
    answer: string | Record<string, any>;
}

const SurveyGroupContainer = ({ pollId, questions }: SurveyGroupContainerProps) => {
    const {
        data: pollResults,
        isLoading,
        error,
    } = useGetPollResults(pollId, {
        staleTime: Infinity,
    });

    if (isLoading) {
        return <Spinner center />;
    }

    if (error) {
        return <div className="text-center">Poll results currently not available.</div>;
    }

    if (!pollResults) {
        return <div className="text-center">No poll results to display.</div>;
    }

    return (
        <div className="survey-group-container">
            <SurveyGroup survey={pollResults} questions={questions} />
        </div>
    );
};

interface SurveyGroupProps {
    survey: PollResult[];
    questions: PollData;
}

interface QuestionResults {
    title: string;
    answers: Record<string, number>;
}

interface Results {
    [key: string]: QuestionResults;
}

const SurveyGroup = ({ survey, questions }: SurveyGroupProps) => {
    const results: Results = {};

    // Initialize results object based on question pages' elements
    questions.pages[0].elements?.forEach(({ name, title, choices }) => {
        results[name] = { title, answers: {} };
        choices?.forEach((choice) => {
            results[name].answers[choice] = 0;
        });
    });

    // Aggregate survey answers, parsing JSON strings if necessary
    survey?.forEach(({ answer }) => {
        let parsedAnswer: Record<string, any> | undefined;
        if (typeof answer === "string") {
            try {
                parsedAnswer = JSON.parse(answer);
            } catch (e) {
                console.error("Error parsing JSON string:", e);
                return;
            }
        } else if (typeof answer === "object" && answer !== null) {
            parsedAnswer = answer as Record<string, any>;
        }

        if (parsedAnswer) {
            Object.keys(parsedAnswer)?.forEach((key) => {
                if (results[key]) {
                    const answerValue = parsedAnswer[key];
                    if (results[key].answers.hasOwnProperty(answerValue)) {
                        results[key].answers[answerValue] += 1;
                    } else {
                        // In case of "other" answers
                        if (!results[key].answers["Other"]) {
                            results[key].answers["Other"] = 0;
                        }
                        results[key].answers["Other"] += 1;
                    }
                }
            });
        }
    });

    const isMobile = useCheckMobileScreen();

    return (
        <div className="mt-4">
            {Object.keys(results).map((question, index) => {
                const questionResults = results[question];
                let labels = Object.keys(questionResults.answers);
                let data = Object.values(questionResults.answers);
                const totalVotes = data.reduce((sum, value) => sum + value, 0);

                // Calculate max height for the chart
                const calculateMaxHeight = (numAnswers: number, heightPerAnswer = 50) => {
                    return numAnswers * heightPerAnswer;
                };
                const maxHeight = calculateMaxHeight(labels.length);
                const labelSize = isMobile ? 10 : 12;

                // Sort labels and data in descending order based on the data values
                const sortedIndices = data
                    .map((value, idx) => ({ value, idx }))
                    .sort((a, b) => b.value - a.value)
                    .map(({ idx }) => idx);

                labels = sortedIndices.map((idx) => labels[idx]);
                data = sortedIndices.map((idx) => data[idx]);

                return (
                    <div
                        key={index}
                        className="font-body text-custom-yellow text-stroke-sm"
                        style={{ marginBottom: "2rem" }}
                    >
                        <h2 className="mb-1">{questionResults.title}</h2>
                        <div
                            style={{
                                backgroundColor: "#1e1e1e",
                                padding: "5px 10px",
                                maxHeight: `${maxHeight}px`,
                                border: "2px solid black",
                                borderRadius: "10px",
                            }}
                        >
                            <Bar
                                data={{
                                    labels,
                                    datasets: [
                                        {
                                            label: "Responses",
                                            data,
                                            backgroundColor: "rgba(0, 128, 0, 0.6)",
                                            borderColor: "rgba(0, 128, 0, 1)",
                                            borderWidth: 1,
                                        },
                                    ],
                                }}
                                options={{
                                    events: [],
                                    indexAxis: "y" as const,
                                    scales: {
                                        x: {
                                            display: false,
                                            grid: {
                                                display: false,
                                            },
                                        },
                                        y: {
                                            ticks: {
                                                color: (context: any) => (context.index === 0 ? "yellow" : "white"),
                                                font: {
                                                    weight: (context: any) => (context.index === 0 ? "bold" : "normal"),
                                                    size: labelSize,
                                                },
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        },
                                    },
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        title: {
                                            display: false,
                                            text: questionResults.title,
                                        },
                                        datalabels: {
                                            formatter: (value: number, context: any) => {
                                                const percentage = ((value / totalVotes) * 100).toFixed(1);
                                                if (value === 0) return null;
                                                return `${percentage}%`;
                                            },
                                            anchor: (context: any) => {
                                                const value = context.dataset.data[context.dataIndex];
                                                const percentage = (value / totalVotes) * 100;
                                                return percentage < 50 ? "start" : "end";
                                            },
                                            align: (context: any) => {
                                                const value = context.dataset.data[context.dataIndex];
                                                const percentage = (value / totalVotes) * 100;
                                                return percentage < 50 ? "end" : "start";
                                            },
                                            color: "white",
                                            font: {
                                                weight: "bold",
                                            },
                                        },
                                    },
                                }}
                            />
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default Results;
