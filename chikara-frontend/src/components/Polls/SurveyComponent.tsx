import { useMemo, useCallback } from "react";
import { Model, surveyLocalization } from "survey-core";
import { Survey } from "survey-react-ui";
import "survey-core/survey-core.css";
import polls from "@/constants/polls.json";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import * as SurveyTheme from "survey-core/themes";
import { useSubmitPollResponse } from "@/features/suggestions/api/usePolls";

// Set up localization once at module level
if (!surveyLocalization.locales["en"]) {
    surveyLocalization.locales["en"] = {};
}
surveyLocalization.locales["en"] = {
    ...surveyLocalization.locales["en"],
    emptySurvey: "Site out of date. Please refresh the page.",
    completeText: "Submit",
};

interface Poll {
    id: number;
    title: string;
    pages: Array<{
        name: string;
        elements: Array<{
            type: string;
            name: string;
            title: string;
            choices?: string[];
            isRequired?: boolean;
            requiredErrorText?: string;
        }>;
    }>;
}

interface SurveyComponentProps {
    poll: Poll;
}

interface SurveyOptions {
    showSaveInProgress: () => void;
    showSaveSuccess: () => void;
    showSaveError: () => void;
}

interface SurveyModel {
    data: Record<string, any>;
}

function SurveyComponent({ poll }: SurveyComponentProps) {
    const navigate = useNavigate();
    const submitPollResponseMutation = useSubmitPollResponse();

    const createPollResponse = useCallback(
        async (pollResponse: string, pollId: number, options: SurveyOptions) => {
            options.showSaveInProgress();
            try {
                const parsedID = parseInt(pollId.toString(), 10);

                await submitPollResponseMutation.mutateAsync({
                    pollId: parsedID,
                    pollResponse,
                });

                options.showSaveSuccess();
                navigate(`/polls?id=${pollId}`);
            } catch (error: any) {
                toast.error(error.message || "Failed to submit poll response");
                options.showSaveError();
                throw error;
            }
        },
        [submitPollResponseMutation, navigate]
    );

    // Initialize the survey model in a Memo to prevent re-creation
    const surveyData = useMemo(() => {
        const survey = new Model(polls[poll.id - 1]);
        survey.applyTheme(SurveyTheme.LayeredDark);
        survey.onComplete.add((sender: SurveyModel, options: SurveyOptions) => {
            const pollResponse = JSON.stringify(sender.data);
            createPollResponse(pollResponse, poll.id, options);
        });

        return survey;
    }, [poll.id, createPollResponse]);

    return <Survey model={surveyData} />;
}

export default SurveyComponent;
