import { cn } from "@/lib/utils";
import React from "react";
import { Link } from "react-router-dom";

interface RoundedButtonProps {
    className?: string;
    href?: string;
    text?: string;
    onClick?: () => void;
    disabled?: boolean;
    textHighlight?: string;
}

const RoundedButton = ({ className, href, text, onClick, disabled = false, textHighlight }: RoundedButtonProps) => {
    return (
        <button
            disabled={disabled}
            className={cn("mt-2 flex select-none flex-col", className)}
            onClick={onClick && (() => onClick())}
        >
            {href ? (
                <Link
                    to={href}
                    className={cn(
                        "roundedBtn block w-full rounded-xl bg-[#f6e58d] px-5 py-2 text-center",
                        disabled ? "brightness-50" : "hover:brightness-105"
                    )}
                >
                    <span>{text}</span>
                </Link>
            ) : (
                <div
                    className={cn(
                        "roundedBtn block w-full cursor-pointer rounded-xl bg-[#f6e58d] px-5 py-2 text-center ",
                        disabled ? "brightness-50" : "hover:brightness-105"
                    )}
                >
                    <span>
                        {text}
                        {textHighlight && (
                            <span className="text-indigo-800 dark:text-indigo-600 font-mono font-semibold">
                                {" "}
                                {textHighlight}
                            </span>
                        )}
                    </span>
                </div>
            )}
        </button>
    );
};

export default RoundedButton;
