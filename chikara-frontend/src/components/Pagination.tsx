interface PaginationProps {
    data?: unknown[];
}

export const Pagination = ({ data }: PaginationProps) => {
    return (
        <div className="mt-auto hidden items-center justify-between border border-gray-200 bg-white px-4 py-3 sm:px-6 md:flex md:rounded-b-lg dark:border-gray-600 dark:bg-gray-800">
            {/* <div className="flex flex-1 justify-between sm:hidden">
        <a
          disabled
          href="#"
          className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-medium text-gray-400 hover:bg-gray-50 md:text-sm"
        >
          Previous
        </a>
        <a
          disabled
          href="#"
          className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-medium text-gray-400 hover:bg-gray-50 md:text-sm"
        >
          Next
        </a>
      </div> */}
            <div className="text-shadow sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                    <p className="text-gray-700 text-xs md:text-sm dark:text-gray-200">
                        Showing <span className="font-medium">1</span> to{" "}
                        <span className="font-medium">{data?.length}</span> of{" "}
                        <span className="font-medium">{data?.length}</span> results
                    </p>
                </div>
                {/* <div>
        <nav
            className="relative z-0 inline-flex -space-x-px rounded-md shadow-xs"
            aria-label="Pagination"
          >
            <a
              disabled
              href="#"
              className="relative inline-flex items-center rounded-l-md border border-gray-300 bg-gray-50 px-2 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:text-sm"
            >
              <span className="sr-only">Previous</span>
            </a>
            <a
              href="#"
              aria-current="page"
              className="relative z-10 inline-flex items-center border border-indigo-500 bg-indigo-50 px-4 py-2 text-xs font-medium text-indigo-600 md:text-sm"
            >
              1
            </a>
            <a
              disabled
              href="#"
              className="relative inline-flex items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:text-sm"
            >
              2
            </a>
            <a
              disabled
              href="#"
              className="relative hidden items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:inline-flex md:text-sm"
            >
              3
            </a>
            <span className="relative inline-flex items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-700 md:text-sm">
              ...
            </span>
            <a
              disabled
              href="#"
              className="relative hidden items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:inline-flex md:text-sm"
            >
              8
            </a>
            <a
              disabled
              href="#"
              className="relative inline-flex items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:text-sm"
            >
              9
            </a>
            <a
              disabled
              href="#"
              className="relative inline-flex items-center border border-gray-300 bg-gray-50 px-4 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:text-sm"
            >
              10
            </a>
            <a
              href="#"
              className="relative inline-flex items-center rounded-r-md border border-gray-300 bg-gray-50 px-2 py-2 text-xs font-medium text-gray-500 hover:bg-gray-50 md:text-sm"
            >
              <span className="sr-only">Next</span>
            </a>
          </nav>
        </div> */}
            </div>
        </div>
    );
};
