import { Link } from "react-router-dom";
import { useSessionStore } from "../../app/store/stores";
import SiteLogo from "./SiteLogo";
import currency2Img from "@/assets/icons/UI/currency2.png";
import yenImg from "@/assets/icons/UI/yen.png";
import chatImg from "@/assets/icons/navitems/chat.png";
import eventsImg from "@/assets/icons/navitems/events.png";
import leaderboardsImg from "@/assets/icons/navitems/leaderboards.png";
import messagesImg from "@/assets/icons/navitems/messages.png";
import newsImg from "@/assets/icons/navitems/news.png";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import newsPosts from "../../constants/news";
import NavIcon from "./NavIcon";
import ShrineBuff from "./ShrineBuff";
import useGetUnreadNotifications from "@/hooks/api/useGetUnreadNotifications";
import useGetUnreadMessages from "@/hooks/api/useGetUnreadMessages";

export default function Navbar() {
    const { data: currentUser } = useFetchCurrentUser();
    const { data: unreadNotifications } = useGetUnreadNotifications();
    const { data: unreadMessages } = useGetUnreadMessages();

    const { hideChat, setHideChat } = useSessionStore();

    const lastNewsIDRead = currentUser?.lastNewsIDRead;
    const unreadNewsCount = newsPosts[0] && lastNewsIDRead ? Math.max(0, newsPosts[0].id - lastNewsIDRead) : 0;

    return (
        <nav className="relative z-10 flex h-[60px] w-full border-[#292c3e] border-b-2 bg-slate-200 px-10 2xl:h-[80px] dark:bg-[#171925]">
            <Link to="/home">
                <SiteLogo />
            </Link>
            <div className="relative flex w-full justify-between gap-5 p-2 xl:pr-80 2xl:pr-96">
                <div></div>

                <div className="my-auto flex gap-5">
                    <ShrineBuff />
                    <div className="mx-auto mr-6 flex flex-row gap-6 2xl:my-auto">
                        <Link to="/bank">
                            <div className="-skew-x-6 moneyFrame relative mt-1 ml-2 flex scale-90 rounded-md border border-slate-600 bg-indigo-600/75 pr-8 pl-3 shadow-xl 2xl:scale-100">
                                <p className="mx-auto my-1 h-7 w-16 skew-x-6 text-center font-lili text-stroke-md text-white text-xl">
                                    {currentUser?.cash}
                                </p>
                                <img
                                    className="-right-5 -top-1 absolute aspect-square h-[2.7rem] skew-x-6"
                                    src={yenImg}
                                    alt=""
                                />
                            </div>
                        </Link>
                        <Link to="/gang">
                            <div className="-skew-x-6 moneyFrame relative mt-1 ml-2 flex scale-90 rounded-md border border-slate-600 bg-yellow-600 pr-8 pl-3 shadow-xl 2xl:scale-100">
                                <p className="mx-auto my-1 h-7 w-12 skew-x-6 text-center font-lili text-stroke-md text-white text-xl">
                                    {currentUser?.gangCreds}
                                </p>
                                <img
                                    className="-right-5 -top-1.5 absolute aspect-square h-12 skew-x-6"
                                    src={currency2Img}
                                    alt=""
                                />
                            </div>
                        </Link>
                    </div>
                    {/* NOTIFICATIONS ICON */}
                    <NavIcon
                        name="Notifications"
                        img={eventsImg}
                        href="/events"
                        notifications={unreadNotifications?.unread}
                    />

                    {/* MAILS ICON */}
                    <NavIcon name="Messages" img={messagesImg} href="/inbox" notifications={unreadMessages?.unread} />
                    {/* LEADERBOARDS ICON */}
                    <NavIcon name="Leaderboards" img={leaderboardsImg} href="/leaderboard" />
                    <NavIcon
                        name="News"
                        img={newsImg}
                        notifSize="sm"
                        href="/news"
                        notifications={unreadNewsCount || false}
                    />
                    {/* <NavIcon
            name="Wiki"
            img={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/MhJCUZW.png`}
            href="https://chikaraacademymmo.wiki.gg/"
            external={true}
          /> */}
                    {/* CHAT OPEN ICON */}
                    {hideChat && <NavIcon name="Unhide Chat" img={chatImg} onClick={() => setHideChat(false)} />}
                </div>
            </div>
        </nav>
    );
}
