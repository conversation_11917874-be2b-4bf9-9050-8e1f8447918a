import { pageScene } from "@/helpers/pageScene";
import { sceneManager } from "@/helpers/sceneManager";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useLocation } from "react-router-dom";
import { useNormalStore, usePersistStore } from "../../../app/store/stores";
import PageHeader from "../PageHeader";

interface BannerDetails {
    scene?: string;
    title?: string;
    description?: string;
}

const PageBanner = () => {
    const location = useLocation();
    const { explorePageSetting } = usePersistStore();
    const { iframeActive } = useNormalStore();
    const isMobile = useCheckMobileScreen();

    let bannerDetails: BannerDetails | null = null;
    if (location.pathname.includes("/shops/")) {
        bannerDetails = pageScene(location.pathname) || {
            scene: "mall1",
            title: "Shops",
        };
    } else {
        bannerDetails = pageScene(location.pathname);
        if (location.pathname.startsWith("/inbox")) {
            if (isMobile) return null;
            bannerDetails = pageScene("/inbox");
        }
        if (location.pathname.startsWith("/news")) {
            bannerDetails = pageScene("/news");
        }
    }

    if (!bannerDetails) return null;

    if (location.pathname === "/explore" && explorePageSetting === "map" && !isMobile) {
        return null;
    }

    if (location.pathname.includes("/shops/") && location.pathname !== "/shops") {
        if (isMobile) {
            return null;
        }
    }

    if (iframeActive && location.pathname.includes("/arcade")) return null;

    // Get the background image
    const backgroundImage = sceneManager(bannerDetails.scene);
    if (!backgroundImage) return null;

    return (
        <div className="relative">
            <PageHeader
                backgroundImage={backgroundImage}
                title={bannerDetails.title}
                subtitle={bannerDetails.description || ""}
            />
        </div>
    );
};

export default PageBanner;
