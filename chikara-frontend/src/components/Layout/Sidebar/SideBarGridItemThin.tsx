import arrow from "@/assets/images/UI/arrow.gif";
import NotificationBadge from "@/components/NotificationBadge";
import StrokedText from "@/components/StrokedText";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";

interface NavItem {
    name: string;
    href: string;
    icon: string;
    current?: string;
    external?: boolean;
    thin?: boolean;
    construction?: boolean;
}

interface SideBarGridItemThinProps {
    item: NavItem;
    inFight?: string | null;
    availableQuests?: number;
}

interface ButtonContentProps {
    item: NavItem;
    inFight?: string | null;
    checkCurrent: (name?: string) => boolean;
    availableQuests?: number;
    displayTutorialArrow?: boolean;
    craftCollectReady?: boolean;
}

const ButtonContent = ({
    item,
    inFight,
    checkCurrent,
    availableQuests,
    displayTutorialArrow = false,
    craftCollectReady,
}: ButtonContentProps) => {
    const tutorialArrow = item.name === "Tasks" && !checkCurrent(item.current) && displayTutorialArrow;

    return (
        <>
            {tutorialArrow && (
                <img
                    className="-rotate-90 -translate-x-1/2 -top-20 absolute left-1/2 z-100 size-24 scale-75"
                    src={arrow}
                    alt=""
                />
            )}
            <div className="max-w-full 2xl:hidden">
                {item.name === "Campus" && craftCollectReady && (
                    <NotificationBadge empty pulse className="absolute top-1 right-[5%] size-4" />
                )}

                {item.name === "Tasks" && availableQuests ? (
                    <NotificationBadge amount={availableQuests} className="h-5! w-5! absolute top-0.5 right-[5%]" />
                ) : null}
            </div>
            <img
                src={item.icon}
                alt=""
                className={cn(
                    `-translate-y-1/2 -translate-x-1/2 absolute top-1/2 left-1/4 z-10 h-[70%] group-hover:scale-105`,
                    (item.construction || inFight) && "brightness-50"
                )}
            />
            <span className={cn("h-2 w-full rounded-md text-white ")}>
                <span
                    className={cn(
                        item.construction && "text-gray-500 text-stroke-sm",
                        "-right-20 -translate-y-1/2 absolute top-[55%] w-full text-left text-base font-normal font-display opacity-90 2xl:top-[45%] 2xl:right-1/2 2xl:translate-x-1/2 2xl:text-center 2xl:group-hover:scale-105",
                        checkCurrent(item.current) ? "text-custom-yellow" : "text-white group-hover:text-yellow-400"
                    )}
                >
                    <StrokedText
                        hideShadow
                        className="font-medium font-accent text-xl text-custom-yellow text-stroke-s-md uppercase"
                    >
                        {item.name}
                    </StrokedText>
                </span>
            </span>
        </>
    );
};

export default function SideBarGridItemThin({ item, inFight, availableQuests }: SideBarGridItemThinProps) {
    const location = useLocation();
    const { craftCollectReady } = useNormalStore();

    const checkCurrent = (name?: string): boolean => {
        if (!name) return false;
        return `/${name}` === location.pathname;
    };

    if (item.external) {
        return (
            <a
                href={item.href}
                target="_blank"
                rel="noreferrer"
                className={cn(
                    "roundedBtnBlueThin group relative col-span-2 flex size-full max-h-12 min-h-7 child-hover:scale-105 rounded-md text-center font-medium font-accent text-stroke-md text-xs drop-shadow-lg hover:brightness-110"
                )}
            >
                <ButtonContent item={item} inFight={inFight} checkCurrent={checkCurrent} />
            </a>
        );
    }

    return (
        <NavLink
            to={!inFight ? item.href : "#"}
            aria-current={checkCurrent(item.current) ? "page" : undefined}
            className={cn(
                checkCurrent(item.current) ? "roundedBtnLightBlueThin" : "roundedBtnBlueThin",
                "group relative col-span-2 flex size-full max-h-12 min-h-7 child-hover:scale-105 rounded-md text-center font-medium font-accent text-stroke-md text-xs drop-shadow-lg hover:brightness-110"
            )}
        >
            <ButtonContent
                item={item}
                inFight={inFight}
                checkCurrent={checkCurrent}
                availableQuests={availableQuests}
                craftCollectReady={craftCollectReady}
            />
        </NavLink>
    );
}
