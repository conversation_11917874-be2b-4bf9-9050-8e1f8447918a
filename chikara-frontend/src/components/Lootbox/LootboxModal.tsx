import { Modal } from "@/components/Modal/Modal";
import useGetDailyChestItems from "@/features/character/api/useGetDailyChestItems";
import { useState } from "react";
import Lootbox from "./Lootbox";

interface LootboxModalProps {
    openModal: boolean;
    setOpenModal: (open: boolean) => void;
}

export default function LootboxModal({ openModal, setOpenModal }: LootboxModalProps) {
    const [isSpin, setIsSpin] = useState<boolean>(false);
    const { data: potentialChestItems, isLoading } = useGetDailyChestItems();
    const handleClose = () => {
        setOpenModal(false);
    };

    return (
        <Modal
            open={openModal}
            showClose={!isSpin}
            modalMaxWidth="md:max-w-fit"
            contentPadding="px-1 md:px-6 py-10"
            iconBackground="shadow-lg"
            contentHeight="overflow-x-hidden!"
            title="Daily Chest"
            Icon={() => (
                <img
                    src="https://d13cmcqz8qkryo.cloudfront.net/static/items/special/dailychest.png"
                    alt=""
                    className="mt-0.5 size-11"
                />
            )}
            onOpenChange={isSpin ? null : handleClose}
        >
            <Lootbox
                potentialChestItems={potentialChestItems}
                isLoading={isLoading}
                isSpin={isSpin}
                setIsSpin={setIsSpin}
                handleClose={handleClose}
            />
        </Modal>
    );
}
