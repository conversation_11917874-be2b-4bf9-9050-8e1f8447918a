import { <PERSON><PERSON><PERSON>, ArrowUp } from "lucide-react";
import { Item, EquippedItems } from "@/types/item";

type ComparisonResult = "same" | boolean;
type ComparisonType = "modifiers" | "damage" | "armour" | "health" | "energy" | "actionPoints";

const isItemBetterThanCurrent = (item: Item, equippedItem: Item, type: ComparisonType): ComparisonResult => {
    if (item?.id === equippedItem?.id) return "same";

    if (type === "modifiers") {
        const itemModifiers = item.statModifiers;
        const equippedItemModifiers = equippedItem.statModifiers;

        // Handle null/undefined cases symmetrically
        if (!itemModifiers && !equippedItemModifiers) return "same";
        if (!itemModifiers && equippedItemModifiers) return false;
        if (itemModifiers && !equippedItemModifiers) return true;

        // Compare all modifiers to determine overall superiority
        let itemBetterCount = 0;
        let equippedBetterCount = 0;

        // Get all unique modifier keys from both items
        const allKeys = new Set([...Object.keys(itemModifiers), ...Object.keys(equippedItemModifiers)]);

        for (const key of allKeys) {
            const itemVal = itemModifiers[key] ? (itemModifiers[key] - 1) * 100 : 0;
            const equippedItemVal = equippedItemModifiers[key] ? (equippedItemModifiers[key] - 1) * 100 : 0;

            if (itemVal > equippedItemVal) {
                itemBetterCount++;
            } else if (itemVal < equippedItemVal) {
                equippedBetterCount++;
            }
        }

        if (itemBetterCount > equippedBetterCount) return true;
        if (equippedBetterCount > itemBetterCount) return false;
        return "same";
    }
    const itemStat = item[type as keyof Item] as number | null;
    const equippedItemStat = equippedItem[type as keyof Item] as number | null;

    if (!itemStat && !equippedItemStat) return "same";
    return (itemStat || 0) > (equippedItemStat || 0);
};

interface ItemComparisonArrowProps {
    item: Item;
    equippedItems?: EquippedItems;
    type?: ComparisonType;
}

export const ItemComparisonArrow = ({ item, equippedItems, type }: ItemComparisonArrowProps) => {
    const equippedItem = equippedItems?.[item.itemType] || null;
    if (!equippedItem || !type) return null;

    const isBetter = isItemBetterThanCurrent(item, equippedItem, type);
    if (isBetter === "same") return null;
    if (isBetter)
        return (
            <ArrowUp className="mb-0.5 ml-1 inline size-5 scale-125 text-green-500 transition-transform duration-200" />
        );
    return <ArrowDown className="mb-0.5 ml-1 inline size-5 scale-125 text-red-500 transition-transform duration-200" />;
};
