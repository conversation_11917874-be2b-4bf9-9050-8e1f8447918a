import { useRegisterSW } from "virtual:pwa-register/react";
import { useRef } from "react";
import { requestNotificationPermission } from "../../app/firebase";

export const ServiceWorkerUpdater = (): null => {
    "use no memo";
    const permissionRequested = useRef(false);

    const { updateServiceWorker: _updateServiceWorker } = useRegisterSW({
        immediate: true,
        onRegisteredSW(swUrl: string, r?: ServiceWorkerRegistration) {
            console.log(`Service Worker at: ${swUrl}`);

            if (r) {
                r.onupdatefound = () => {
                    console.log("New update found and is being installed.");
                };

                const updateInterval = setInterval(() => {
                    console.log("Checking for updates...");
                    try {
                        r.update();
                    } catch (error) {
                        console.error("Error during service worker update:", error);
                        clearInterval(updateInterval); // Stop trying to update if an error occurs
                    }
                }, 100000);

                if (r?.active?.state === "activated") {
                    if (!permissionRequested.current) {
                        permissionRequested.current = true;
                        requestNotificationPermission(r);
                    }
                } else {
                    console.log("Service Worker is not in the activated state:", r?.active?.state);
                }
            } else {
                console.error("Service worker registration object is null or undefined.");
            }
        },
        onRegisterError(error: Error) {
            console.error("Service worker registration error:", error);
        },
    });

    return null;
};
