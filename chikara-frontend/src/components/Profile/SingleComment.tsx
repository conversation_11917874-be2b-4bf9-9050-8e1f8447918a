import { DisplayAvatar } from "@/components/DisplayAvatar";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Link } from "react-router-dom";
import { formatTimeToNow } from "@/helpers/dateHelpers";

interface Comment {
    message: string;
    createdAt: string;
    senderId: number;
}

interface SingleCommentProps {
    comment: Comment;
}

export default function SingleComment({ comment }: SingleCommentProps) {
    const { data: user } = useGetUserInfo(comment?.senderId);

    return (
        <li>
            <div className="flex space-x-3">
                <div className="shrink-0">
                    <DisplayAvatar className="size-10 rounded-full" src={user} />
                </div>
                <div>
                    <div className="text-sm">
                        <Link
                            to={"/profile/" + comment?.senderId}
                            className="font-medium text-gray-900 dark:text-gray-100"
                        >
                            {user?.username}
                        </Link>
                    </div>
                    <div className="mt-1 whitespace-pre-wrap text-gray-700 text-sm dark:text-gray-300">
                        <p>{comment.message}</p>
                    </div>
                    <div className="mt-2 space-x-2 text-sm">
                        <span className="font-medium text-gray-500 dark:text-gray-400">
                            {formatTimeToNow(comment?.createdAt)} ago
                        </span>{" "}
                        {/* ?Future reply functionality? */}
                        {/* <span className="font-medium text-gray-500">&middot;</span>{" "} */}
                        {/* <button type="button" className="font-medium text-gray-900">
              Reply
            </button> */}
                    </div>
                </div>
            </div>
        </li>
    );
}
