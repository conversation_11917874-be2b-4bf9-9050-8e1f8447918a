import { api } from "@/helpers/api";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { Portal } from "react-portal";
import type { TalentInfo, UserTalent } from "@/features/talents/types/talents";

interface TalentInfoModalProps {
    selectedTalent: TalentInfo;
    setSelectedTalent: (talent?: TalentInfo | null) => void;
    treePoints?: number;
    unlockedTalents?: UserTalent[];
    talentPoints?: number;
    isTalentDisabled?: boolean;
}

export default function TalentInfoModal({
    selectedTalent,
    setSelectedTalent,
    treePoints,
    unlockedTalents,
    talentPoints,
    isTalentDisabled,
}: TalentInfoModalProps) {
    const queryClient = useQueryClient();
    const talentPointsRemaining = () => {
        if (selectedTalent?.pointsInTreeRequired > (treePoints || 0)) {
            return selectedTalent.pointsInTreeRequired - (treePoints || 0);
        } else {
            return 0;
        }
    };

    const talentUnlocked = () => {
        const unlockedTalent = unlockedTalents?.find((talent) => talent?.talentId === selectedTalent?.id);
        return unlockedTalent || null;
    };

    const [unlockedTalent, setUnlockedTalent] = useState<UserTalent | null>(null);

    useEffect(() => {
        setUnlockedTalent(talentUnlocked());
    }, [unlockedTalents, selectedTalent?.id]);

    const isPurchaseDisabled = () => {
        if (isTalentDisabled) return true;
        if (talentPointsRemaining() > 0) return true;
        if ((talentPoints || 0) === 0) return true;
        return false;
    };

    const isTalentMaxed = () => {
        if (selectedTalent?.talentType === "locked") return false;
        if (unlockedTalent?.level === selectedTalent?.maxPoints) return true;
        else return false;
    };

    const unlockTalent = useMutation(
        api.talents.unlockTalent.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.talents.getUnlockedTalents.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                toast.success("Talent Unlocked!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    const handleUnlockTalent = () => {
        if (selectedTalent?.disabled) {
            toast.error("This talent is temporarily disabled!");
            return;
        }
        unlockTalent.mutate({ talentId: selectedTalent?.id });
    };

    return (
        <Portal>
            <motion.div
                initial={{ y: "100%" }}
                animate={{ y: 0 }}
                transition={{ ease: "easeIn", duration: 0.01 }}
                className={cn(
                    isTalentMaxed() ? "border-green-600" : "border-blue-600",
                    "dark talentInfoModalAnim fixed inset-x-0 bottom-0 z-300 min-h-[20%] w-full transform-none overflow-y-auto border-2 bg-gray-800 px-4 pt-1 pb-4 text-stroke-sm transition-transform md:bottom-5 md:left-[31.5%] md:w-1/3"
                )}
            >
                {selectedTalent.pointsCost && selectedTalent.pointsCost > 0 && (
                    <div className="absolute right-4 bottom-2 flex gap-2 text-3xl text-white">
                        <span className="font-accent text-purple-500">{selectedTalent.pointsCost}</span>
                        <img
                            className="inline h-10 w-auto"
                            src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                            alt="Talent Points"
                        />
                    </div>
                )}
                <h5
                    className={cn(
                        "mt-0.5 mb-1 inline-flex items-center text-2xl uppercase",
                        selectedTalent?.talentType === "locked"
                            ? "text-gray-500"
                            : "text-indigo-600 dark:text-indigo-500"
                    )}
                >
                    <motion.div
                        key={selectedTalent?.image}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                    >
                        <img
                            alt=""
                            src={displayMissingIcon(selectedTalent?.image)}
                            className={cn(
                                "mr-2.5 h-14 rounded-md text-gray-500 dark:text-gray-200",
                                selectedTalent?.talentType === "locked" ? "w-10 brightness-90" : "w-14"
                            )}
                        />
                    </motion.div>
                    <div>
                        {selectedTalent?.talentType === "ability" && (
                            <p className="text-orange-400 text-stroke-sm text-xs">Ability</p>
                        )}
                        {selectedTalent?.talentType === "passive" && (
                            <p className="text-sky-400 text-stroke-sm text-xs">Passive</p>
                        )}
                        <span className="font-display font-bold">
                            {selectedTalent?.displayName ? selectedTalent?.displayName : selectedTalent?.name}
                        </span>
                        {selectedTalent?.maxPoints && (
                            <span className="ml-3 text-blue-500 dark:text-blue-300 font-mono font-bold">
                                {unlockedTalent ? unlockedTalent?.level : 0}/{selectedTalent?.maxPoints}
                            </span>
                        )}
                    </div>
                </h5>
                <button
                    type="button"
                    className="absolute top-2.5 right-2.5 inline-flex size-8 items-center justify-center rounded-lg bg-gray-600 text-slate-200 text-sm hover:bg-gray-200 hover:text-gray-900 md:bg-transparent dark:hover:bg-gray-600 dark:hover:text-white"
                    onClick={() => setSelectedTalent()}
                >
                    <svg
                        className="size-3 "
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 14 14"
                    >
                        <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                        />
                    </svg>
                    <span className="sr-only">Close menu</span>
                </button>
                <p className="max-w-lg text-gray-500 text-sm dark:text-gray-200">
                    {typeof selectedTalent?.description === "function"
                        ? selectedTalent.description(selectedTalent)
                        : selectedTalent?.description}
                </p>
                {selectedTalent?.subDescription && (
                    <p className="max-w-lg text-gray-500 text-sm dark:text-gray-300">
                        {typeof selectedTalent?.subDescription === "function"
                            ? selectedTalent.subDescription(selectedTalent)
                            : selectedTalent?.subDescription}
                    </p>
                )}
                {selectedTalent?.staminaCost > 0 && (
                    <p className="max-w-lg text-orange-400 text-sm dark:text-orange-400">
                        {selectedTalent.staminaCost} STA
                    </p>
                )}
                {selectedTalent?.level1Desc && (
                    <p className="mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200">
                        <span
                            className={cn(
                                "mr-2 font-display font-bold",
                                unlockedTalent?.level > 0 ? "text-green-600 dark:text-green-500" : "text-blue-500"
                            )}
                        >
                            Level 1:
                        </span>
                        {typeof selectedTalent?.level1Desc === "function"
                            ? selectedTalent.level1Desc(selectedTalent)
                            : selectedTalent?.level1Desc}
                    </p>
                )}
                {selectedTalent?.level2Desc && (
                    <p className="mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200">
                        <span
                            className={cn(
                                "mr-2 font-display font-bold",
                                unlockedTalent?.level > 1 ? "text-green-600 dark:text-green-500" : "text-blue-500"
                            )}
                        >
                            Level 2:
                        </span>
                        {typeof selectedTalent?.level2Desc === "function"
                            ? selectedTalent.level2Desc(selectedTalent)
                            : selectedTalent?.level2Desc}
                    </p>
                )}
                {selectedTalent?.level3Desc && (
                    <p className="mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200">
                        <span
                            className={cn(
                                "mr-2 font-display font-bold",
                                unlockedTalent?.level > 2 ? "text-green-600 dark:text-green-500" : "text-blue-500"
                            )}
                        >
                            Level 3:
                        </span>
                        {typeof selectedTalent?.level3Desc === "function"
                            ? selectedTalent.level3Desc(selectedTalent)
                            : selectedTalent?.level3Desc}
                    </p>
                )}
                {selectedTalent?.talentType !== "locked" && !isTalentMaxed() ? (
                    <div className="mt-6 flex w-full flex-col">
                        {talentPointsRemaining() > 0 && (
                            <p className="-mt-3 mx-auto mb-2 text-red-700 text-sm">
                                Requires {talentPointsRemaining()} more talent points spent in Strength
                            </p>
                        )}
                        <button
                            disabled={isPurchaseDisabled()}
                            className={cn(
                                "font-display mx-auto rounded-lg border border-gray-900 px-4 py-2 text-center font-medium text-base text-stroke-sm! focus:outline-hidden focus:ring-4",
                                isPurchaseDisabled()
                                    ? "bg-gray-500 text-gray-400"
                                    : "bg-blue-700 text-white hover:bg-blue-800 focus:ring-blue-300 dark:bg-blue-600 dark:focus:ring-blue-800 dark:hover:bg-blue-700"
                            )}
                            onClick={() => (isPurchaseDisabled() ? null : handleUnlockTalent())}
                        >
                            Purchase
                        </button>
                    </div>
                ) : null}
            </motion.div>
        </Portal>
    );
}
