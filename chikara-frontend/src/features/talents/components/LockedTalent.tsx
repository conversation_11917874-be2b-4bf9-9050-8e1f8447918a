import lockedImg from "@/assets/icons/talents/locked.png";
import { cn } from "@/lib/utils";
import type React from "react";
import type { TalentInfo } from "@/features/talents/types/talents";

interface LockedTalentProps {
    talent?: TalentInfo | null;
    setSelectedTalent: (talent?: TalentInfo | null) => void;
    selectedTalent?: TalentInfo | null;
    isTalentDisabled?: boolean;
}

export default function LockedTalent({ talent, setSelectedTalent, selectedTalent, isTalentDisabled }: LockedTalentProps) {
    const lockedTalent = {
        id: 0,
        name: "LOCKED",
        image: lockedImg,
        description: "This talent is not available in Alpha.",
        talentType: "locked",
    };
    const handleClick = (e: React.MouseEvent) => {
        if (selectedTalent?.name === "LOCKED") {
            setSelectedTalent();
        } else {
            setSelectedTalent(lockedTalent);
        }
    };
    return (
        <div
            className={cn(isTalentDisabled && "brightness-50 grayscale", "relative m-auto flex h-3/4 drop-shadow-xl")}
            onClick={(e) => handleClick(e)}
        >
            <img
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/lockBG.png`}
                className="m-auto max-h-full cursor-pointer"
                alt=""
            />
            <img
                className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 m-auto max-h-[40%] cursor-pointer brightness-125"
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/lock.png`}
                alt=""
            />
            {talent?.connectedAbove && <ArrowDown />}
        </div>
    );
}

const ArrowDown = () => {
    return (
        <div className="-translate-x-1/2 -top-13 absolute left-1/2 h-3/5 w-4 border-gray-600 border-x-2 bg-amber-300">
            {/* // ARROWHEAD // */}
            <div className="-translate-x-1/2 -bottom-5 absolute left-1/2 inline-block w-7 overflow-hidden drop-shadow-lg">
                <div className="-rotate-45 size-5 origin-top-left border border-gray-600 bg-amber-300"></div>
            </div>
        </div>
    );
};
