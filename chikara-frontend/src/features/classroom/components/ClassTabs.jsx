import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, ClipboardList, MessageSquare, ShoppingBasket, Users } from "lucide-react";
import { Fragment } from "react";

export default function ClassTabs({ selectedTab, setSelectedTab }) {
    const tabs = [
        { value: "exams", label: "Exams", icon: <ClipboardList className="h-6 w-auto lg:h-5" /> },
        {
            value: "members",
            label: "Members",
            icon: <Users className="h-6 w-auto lg:h-5" />,
        },
        {
            value: "chat",
            label: "Chat",
            icon: <MessageSquare className="h-6 w-auto lg:h-5" />,
        },
        {
            value: "rankings",
            label: "Rankings",
            icon: <BarChart className="h-6 w-auto lg:h-5" />,
        },
        {
            value: "shop",
            label: "Shop",
            icon: <ShoppingBasket className="h-6 w-auto lg:h-5" />,
        },
    ];

    const handleTabsChange = (value) => {
        setSelectedTab(value);
    };

    return (
        <div className="flex flex-row px-[20px] pb-0">
            <div className="flex min-h-[48px]">
                <div className="relative mb-0 inline-block w-full flex-1 overflow-visible whitespace-nowrap">
                    <div className="flex" role="tablist">
                        {tabs.map((tab) => (
                            <Fragment key={tab.value}>
                                <Tab
                                    icon={tab.icon}
                                    label={tab.label}
                                    value={tab.value}
                                    isSelected={tab.value === selectedTab}
                                    handleTabsChange={handleTabsChange}
                                    disabled={tab.disabled}
                                />
                            </Fragment>
                        ))}
                    </div>
                    <span className="left-0 w-[113.453px]"></span>
                </div>
            </div>
        </div>
    );
}

const Tab = ({ label, icon, isSelected = false, value, handleTabsChange, disabled }) => {
    return (
        <button
            tabIndex="-1"
            type="button"
            role="tab"
            aria-selected="false"
            className={cn(
                "flex-row! gap-3! flex text-white",
                isSelected ? "bg-[#1C2125]! testSelectedTab mb-[-0.5px]! overflow-visible!" : "testTab",
                disabled && "text-gray-500! cursor-not-allowed opacity-50"
            )}
            onClick={() => (disabled ? null : handleTabsChange(value))}
        >
            {icon} <span className="hidden lg:block">{label}</span>
        </button>
    );
};
