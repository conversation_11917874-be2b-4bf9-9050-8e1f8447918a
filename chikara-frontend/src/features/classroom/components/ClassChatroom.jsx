import { useSocketStore } from "@/app/store/stores";
import Chatbox from "@/features/chat/Chatbox";
import { useEffect } from "react";

const ClassChatroom = ({ currentChatroom, currentClass }) => {
    const { socket } = useSocketStore();

    useEffect(() => {
        if (socket) {
            if (currentChatroom) {
                socket.emit("join room", currentChatroom?.id);
            }
            return () => {
                if (currentChatroom) {
                    socket.emit("leave room", currentChatroom?.id);
                }
            };
        }
    }, [currentChatroom, socket]);

    if (!currentChatroom) return null;
    return (
        <div className="">
            <Chatbox
                hideHeader
                fullSize
                chatRoom={currentChatroom}
                customHeight="lg:h-[calc(100dvh-18.5rem)]! h-[calc(100dvh-15.5rem)]! mt-0!"
            />
        </div>
    );
};

export default ClassChatroom;
