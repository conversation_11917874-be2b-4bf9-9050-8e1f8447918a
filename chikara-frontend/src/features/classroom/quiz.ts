interface AppLocale {
    landingHeaderText: string;
    question: string;
    startQuizBtn: string;
    resultFilterAll: string;
    resultFilterCorrect: string;
    resultFilterIncorrect: string;
    prevQuestionBtn: string;
    nextQuestionBtn: string;
    resultPageHeaderText: string;
}

interface QuizQuestion {
    question: string;
    answerSelectionType: "single";
    questionType: "text";
    answers: (string | number)[];
    correctAnswer: string;
    point: string;
}

interface Quiz {
    quizTitle: string;
    progressBarColor: string;
    appLocale: AppLocale;
    quizSynopsis: string;
    nrOfQuestions: string;
    questions: QuizQuestion[];
}

export const quiz1: Quiz = {
    quizTitle: "Exam 1: General Knowledge",
    progressBarColor: "#9de1f6",
    appLocale: {
        landingHeaderText: "<questionLength> Questions",
        question: "Question",
        startQuizBtn: "Start Exam",
        resultFilterAll: "All",
        resultFilterCorrect: "Correct",
        resultFilterIncorrect: "Incorrect",
        prevQuestionBtn: "Prev",
        nextQuestionBtn: "Next",
        resultPageHeaderText:
            "You have completed the quiz. You got <correctIndexLength> out of <questionLength> questions.",
    },
    quizSynopsis:
        "You will have 80 seconds to complete the exam. Navigating away from the exam will score you 0 points. You can only take the exam once.",
    nrOfQuestions: "10",
    questions: [
        {
            question: "What is the name of the Academy Disciplinary Overseer",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Haruka Ito", "Ayumi Fujimoto", "Kazuya Takahashi", "Haruto Watanabe"],
            correctAnswer: "1",
            point: "1",
        },
        {
            question: "What is the name of the game playable in the Arcade",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Terraformer", "Faceminer", "PAC-MAN", "Spaceminer"],
            correctAnswer: "1",
            point: "1",
        },
        {
            question: "What is the name of the Shopkeeper that appears on Sundays",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Otake", "Honda", "Goda", "Honoo"],
            correctAnswer: "2",
            point: "1",
        },
        {
            question: "Which 3 items in a row give a 4x multiplier in the slot machine",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Burger", "Tuna", "Cola", "Donut"],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "What is the name of the dog of healing",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Astro", "Atlas", "Ares", "Apollo"],
            correctAnswer: "4",
            point: "1",
        },
        {
            question: "What is the name of the corporation from which you can get a part-time job?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Gishnib Corp", "PGS Corp", "CAS Corp", "BFD Corp"],
            correctAnswer: "2",
            point: "1",
        },
        {
            question: "What is the location of your Town House property",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Akihabara", "Shibuya", "Harajuku", "Shinjuku"],
            correctAnswer: "2",
            point: "1",
        },
        {
            point: "1",
            question: "What is the name of the first (weakest) boss available on the rooftop battles",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Katsuro Yamamoto", "Miyuki Tanaka", "Yuto Nakajima", "Kazuya Takahashi"],
            correctAnswer: "3",
        },
        {
            point: "1",
            question: "Can Godzilla survive entering a black hole?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Yes", "No"],
            correctAnswer: "1",
        },
        {
            point: "1",
            question: "Which of the following features was NOT available at the launch of this Alpha",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Shopkeeper Reputation", "Shrine", "Bounty Board", "Courses"],
            correctAnswer: "2",
        },
    ],
};

export const quiz2: Quiz = {
    quizTitle: "Exam 2: Combat & Adventure Mode",
    progressBarColor: "#9de1f6",
    appLocale: {
        landingHeaderText: "<questionLength> Questions",
        question: "Question",
        startQuizBtn: "Start Exam",
        resultFilterAll: "All",
        resultFilterCorrect: "Correct",
        resultFilterIncorrect: "Incorrect",
        prevQuestionBtn: "Prev",
        nextQuestionBtn: "Next",
        resultPageHeaderText:
            "You have completed the quiz. You got <correctIndexLength> out of <questionLength> questions.",
    },
    quizSynopsis:
        "You will have 125 seconds to complete the exam. Navigating away from the exam will score you 0 points. You can only take the exam once.",
    nrOfQuestions: "10",
    questions: [
        {
            question: "At what level do you lose your new player PvP protection?",
            answerSelectionType: "single",
            questionType: "text",
            answers: [4, 5, 6, 7],
            correctAnswer: "2",
            point: "1",
        },
        {
            question: "What was the level cap when the Alpha was launched?",
            answerSelectionType: "single",
            questionType: "text",
            answers: [40, 35, 30, 25],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "What is the base effect of the Stamina Stat?",
            answerSelectionType: "single",
            questionType: "text",
            answers: [
                "Increases max combat stamina by 5 for every 50 stamina points",
                "Increases max combat stamina by 10 for every 150 stamina points",
                "Increases max combat stamina by 5 for every 100 stamina points",
                "Increases max combat stamina by 10 for every 100 stamina points",
            ],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "Which NPC places bounties on students?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Ayumi Fujimoto", "Miyuki Tanaka", "Kazuya Takahashi", "Haruka Ito"],
            correctAnswer: "4",
            point: "1",
        },
        {
            question: "Which item do you have to buy and equip in the tutorial?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Ruler", "Pen", "Pencil", "Scissors"],
            correctAnswer: "2",
            point: "1",
        },
        {
            question: "What is the name of the boss on Streets Zone 1?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Super Practice Dummy", "Super Training Dummy", "Disturbed Student", "Nervous Student"],
            correctAnswer: "1",
            point: "1",
        },
        {
            question: "Which of these creatures can be found in the Sewers?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Sewer Beast", "Mr Dobby", "Sewer Nightmare", "Sewer Hound"],
            correctAnswer: "4",
            point: "1",
        },
        {
            point: "1",
            question: "Which skill deals 30% of the targets max HP as damage?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Giant Slingshot", "Headbutt", "Spray", "Rend"],
            correctAnswer: "2",
        },

        {
            point: "1",
            question: "What does the Bloodlust talent do?",
            answerSelectionType: "single",
            questionType: "text",
            answers: [
                "Inflict more damage on low health targets",
                "Regenerates HP in combat",
                "Gives a melee damage bonus",
                "Increases damage each turn in combat",
            ],
            correctAnswer: "2",
        },
        {
            point: "1",
            question: "How much max health does the Recovery skill restore each turn?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["5%", "10%", "15%", "20%"],
            correctAnswer: "3",
        },
    ],
};

export const quiz3: Quiz = {
    quizTitle: "Exam 3: Godzilla Lore",
    progressBarColor: "#9de1f6",
    appLocale: {
        landingHeaderText: "<questionLength> Questions",
        question: "Question",
        startQuizBtn: "Start Exam",
        resultFilterAll: "All",
        resultFilterCorrect: "Correct",
        resultFilterIncorrect: "Incorrect",
        prevQuestionBtn: "Prev",
        nextQuestionBtn: "Next",
        resultPageHeaderText:
            "You have completed the quiz. You got <correctIndexLength> out of <questionLength> questions.",
    },
    quizSynopsis:
        "You will have 125 seconds to complete the exam. Navigating away from the exam will score you 0 points. You can only take the exam once.",
    nrOfQuestions: "10",
    questions: [
        {
            question: "What is the name of Godzillas adopted son?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Gamera", "Minilla", "Rodan", "Mothra"],
            correctAnswer: "2",
            point: "1",
        },
        {
            question: "Which month did Chikara Alpha 2 launch?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["March", "June", "April", "May"],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "If Godzilla had his own unique combat skill in Chikara Academy, what would it be?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Fire breath", "Laser eyes", "Atomic breath", "Thunder roar"],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "Which old browser game was Chikara Academy inspired by?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Godzilla Wars", "Sentou Gakuen", "Torn City", "Tokyo Heroes"],
            correctAnswer: "2",
            point: "1",
        },

        {
            question: "What is the amount of stat you gain from completing one of the highest tier courses?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["400", "450", "500", "350"],
            correctAnswer: "1",
            point: "1",
        },
        {
            question:
                "What was one of the new features added in Chikara Alpha 2 that did not exist in the previous Alpha test?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Leaderboards", "Adventure Mode", "Part-Time Job", "Combat Skills"],
            correctAnswer: "4",
            point: "1",
        },
        {
            question: "Which of the following is NOT a potential Shrine buff?",
            answerSelectionType: "single",
            questionType: "text",
            answers: [
                "Reduced injury chance",
                "Increases rare item drops",
                "Increased armor",
                "Reduced mission duration",
            ],
            correctAnswer: "1",
            point: "1",
        },
        {
            question: "What would be the total listing fee to list a stack of 2 Pens on the market for 48 hours?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["9%", "8%", "18%", "12%"],
            correctAnswer: "1",
            point: "1",
        },
        {
            question: "Which item is required in order to create a Gang?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Gang Token", "Gang Credit", "Gang Sigil", "Gang Charter"],
            correctAnswer: "3",
            point: "1",
        },
        {
            question: "Which of the following items is required to craft a Chest of Fate?",
            answerSelectionType: "single",
            questionType: "text",
            answers: ["Oreite Ingot", "Infused Fate Core", "Gishnib Bishlab", "Toolbox"],
            correctAnswer: "4",
            point: "1",
        },
    ],
};
