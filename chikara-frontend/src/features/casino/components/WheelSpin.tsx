import { useState } from "react";

export default function WheelSpin() {
    const [isRotating, setIsRotating] = useState(true);

    const stopRotation = () => {
        setIsRotating(false);
    };

    return (
        <div className="md:-mx-4 md:-mt-2 mt-2 md:px-8 md:pb-8 dark:text-white">
            <div className="relative mt-5 size-full">
                <img
                    className="mx-auto w-1/2"
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}essut.workers.dev/ui-images/STKNxsn.png`}
                    alt=""
                />
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}essut.workers.dev/ui-images/N7mNfdI.png`}
                    alt=""
                    className={`absolute top-[5%] left-[27.5%] w-[45%] ${isRotating ? "rotate" : "endrotate"}`}
                />
            </div>
            <button onClick={stopRotation}>Stop Rotation</button>
        </div>
    );
}
