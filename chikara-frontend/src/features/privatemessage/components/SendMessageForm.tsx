import emojiImg from "@/assets/icons/UI/emojiButton.png";
import sendImg from "@/assets/icons/UI/sendButton.png";
import { api } from "@/helpers/api";
import useOuterClick from "@/hooks/useOuterClick";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AnimatePresence } from "framer-motion";
import React, { useState } from "react";
import { toast } from "react-hot-toast";
import EmotePicker from "../../chat/components/EmotePicker";
import { SendMessageFormProps } from "../types/privateMessage";

export default function SendMessageForm({ userId, currentUser }: SendMessageFormProps) {
    const [userMessage, setUserMessage] = useState("");
    const [noMessageError, setNoMessageError] = useState(false);

    const queryClient = useQueryClient();
    const [openEmoji, setOpenEmoji] = useState(false);

    const innerRef = useOuterClick(() => {
        setOpenEmoji(false);
    });

    const sendMessage = useMutation(
        api.messaging.sendMessage.mutationOptions({
            onSuccess: () => {
                setUserMessage("");
                queryClient.invalidateQueries({
                    queryKey: api.messaging.getChatHistory.key(),
                });
            },
            onError: (error) => {
                console.error("Sending message failed:", error);
                toast.error(error.message || "Failed to send message");
            },
        })
    );

    const handleSubmit = () => {
        if (userMessage.length > 0 && userId) {
            sendMessage.mutate({
                userId: userId,
                message: userMessage,
            });
        } else {
            setNoMessageError(true);
            toast.error("Message can't be blank!");
            setTimeout(() => setNoMessageError(false), 2000);
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (event.key === "Enter") {
            event.preventDefault();
            handleSubmit();
        }
    };

    return (
        <div
            className={cn(
                noMessageError ? `chatMessageBoxError` : `chatMessageBox`,
                "-translate-x-1/2 absolute bottom-0 left-1/2 mb-4 flex w-[96%] justify-between rounded-md text-center text-sm shadow-xs md:my-3 md:w-5/6 dark:bg-[#15121C]"
            )}
        >
            <div className="flex w-full flex-row">
                <textarea
                    required
                    id="privateMessage"
                    name="privateMessage"
                    maxLength={currentUser?.userType === "admin" ? 2000 : 200}
                    rows={2}
                    cols={1}
                    className="chatTextArea scrollbar h-16 whitespace-pre-wrap rounded-md border-none bg-[#dee2e9] p-2 font-mono text-sm md:h-auto dark:bg-[#15121C] dark:text-white"
                    placeholder="Your message..."
                    value={userMessage}
                    onChange={(e) => setUserMessage(e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e)}
                />
                <div
                    className="relative mx-1 my-auto ml-auto"
                    onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        setOpenEmoji((prev) => !prev);
                    }}
                >
                    <ButtonWrapper>
                        <img src={emojiImg} alt="" className="size-6 fill-white text-white" />
                    </ButtonWrapper>
                </div>
                <AnimatePresence>
                    {openEmoji && (
                        <EmotePicker
                            userMessage={userMessage}
                            setUserMessage={setUserMessage}
                            innerRef={innerRef}
                            // currentUser={currentUser}
                        />
                    )}
                </AnimatePresence>
                <div className="relative my-auto mr-2 ml-1" onClick={handleSubmit}>
                    <ButtonWrapper>
                        <img src={sendImg} alt="" className="h-5 w-4 fill-white text-white" />
                    </ButtonWrapper>
                </div>
            </div>
        </div>
    );
}

const ButtonWrapper = React.memo(({ children }: { children: React.ReactNode }) => (
    <button className="size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28287c] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110">
        <div className="flex items-center justify-center">{children}</div>
    </button>
));
