import { User } from "@/types/user";

// Private message interface based on the database schema
export interface PrivateMessage {
    id: number;
    message: string;
    read: boolean | null;
    isGlobal: boolean | null;
    createdAt: string;
    updatedAt: string;
    senderId: number | null;
    receiverId: number | null;
}

// Grouped message structure used in Inbox component
export interface GroupedMessage {
    senderId: string;
    messages: PrivateMessage[];
    unreadTotal: number;
}

// Props interfaces for components
export interface MessagePreviewProps {
    sender: GroupedMessage;
    currentConvoId: string | undefined;
    currentUserId: number | undefined;
}

export interface MessageWindowProps {
    sortedArray: GroupedMessage[];
    sentMessages: PrivateMessage[] | undefined;
    currentUser: User | undefined;
    convoId: string | undefined;
}

export interface PrivateMessageProps {
    message: PrivateMessage;
    type?: "sentMessage" | "receivedMessage";
    convoUserInfo: User;
    currentUser: User;
}

export interface SendMessageFormProps {
    userId: number | undefined;
    currentUser: User | undefined;
}

export interface MessageDropdownMenuProps {
    convoUserInfo: User;
}

// API mutation input types
export interface SendMessageInput {
    userId: number;
    message: string;
}

export interface MarkMessageReadInput {
    messageId: number;
}
