import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

interface HomeNavButtonProps {
    title: string;
    talentPoints?: number;
    background: string;
    icon: string;
    link: string;
    lockedLevel?: number;
    maxHeight?: string;
    inlineIcon?: boolean;
}

function HomeNavButton({ title, talentPoints, background, icon, link, lockedLevel, maxHeight, inlineIcon }: HomeNavButtonProps) {
    const isLocked = lockedLevel;
    return (
        <Link
            className={cn(isLocked && "cursor-default", "group size-full rounded-md", maxHeight)}
            to={!isLocked && link}
        >
            <div
                style={{ backgroundImage: `url(${background})` }}
                className={cn(
                    `relative flex h-full rounded-md border-2 border-black bg-center bg-cover`,
                    isLocked ? "grayscale" : "group-hover:brightness-110"
                )}
            >
                <div
                    className={cn(
                        "flex size-full gap-1 rounded-md p-4 backdrop-blur-[1px] md:justify-between md:gap-0 md:px-14 md:py-0",
                        inlineIcon ? "mr-5 flex-row justify-center gap-3" : "flex-col md:flex-row"
                    )}
                >
                    {talentPoints && !isLocked ? (
                        <div className="absolute top-0.5 right-1 flex gap-1 rounded-lg bg-gray-800/50 px-1 text-center text-blue-400 text-stroke-sm md:hidden">
                            <p className="text-lg">{talentPoints}</p>
                            <img
                                className="my-auto h-5 w-auto"
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                                alt="Talent Points"
                            />
                        </div>
                    ) : null}
                    <img
                        src={icon}
                        alt=""
                        className={cn(
                            "w-9 md:m-auto 2xl:w-12",
                            !isLocked && "group-hover:scale-[1.15]",
                            inlineIcon ? "my-auto md:m-auto" : "mx-auto"
                        )}
                    />
                    <div
                        className={cn(
                            "z-40 flex flex-col rounded-b-md text-center font-accent text-custom-yellow text-stroke-sm text-xl uppercase md:my-auto md:w-full md:p-2 2xl:text-[1.65rem] ",
                            inlineIcon ? "text-2xl! my-auto!" : "mx-auto",
                            isLocked ? "-mt-1" : ""
                        )}
                    >
                        <span className={cn("text-custom-yellow", !isLocked && "group-hover:brightness-95")}>
                            {title}
                        </span>
                        {talentPoints && !isLocked ? (
                            <div className="m-auto mt-1 hidden items-center rounded-full border border-gray-600/50 bg-gray-800/50 px-2 py-0 text-center text-blue-400 text-lg text-stroke-sm shadow-lg md:inline-flex">
                                <p className="mt-1 text-xl md:mt-0">{talentPoints}</p>
                                <img
                                    className="ml-2 h-5 w-auto md:mt-0.5"
                                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                                    alt="Talent Points"
                                />
                            </div>
                        ) : null}
                        {isLocked && (
                            <div className="-mt-1 mx-auto flex flex-row text-center font-lili text-slate-100 text-stroke-sm text-xs md:text-base">
                                Requires level {lockedLevel}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Link>
    );
}

export default HomeNavButton;
