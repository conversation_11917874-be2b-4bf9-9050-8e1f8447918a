/* eslint-disable react-hooks/react-compiler */
import { Modal } from "@/components/Modal/Modal";
import { api } from "@/helpers/api";
import { statusEffects } from "@/helpers/statusEffects";
import useGameConfig from "@/hooks/useGameConfig";
import { useState } from "react";
import toast from "react-hot-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { Item } from "@/types/item";
import type { User } from "@/types/user";

interface SpecialItemModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    title: string;
    item: Item;
    currentUser: User;
}

export default function SpecialItemModal({ open, setOpen, title, item, currentUser }: SpecialItemModalProps) {
    const [targetId, setTargetId] = useState<number | null>(null);
    const [reason, setReason] = useState<string>("");
    const [injuryType, setInjuryType] = useState<string>("accuracy");
    const uniqueItemsConfig = useGameConfig();
    const queryClient = useQueryClient();

    const { HOSPITALISE_ITEM_NAME, REVIVE_ITEM_NAME, JAIL_ITEM_NAME, MEGAPHONE_ITEM_NAME } = uniqueItemsConfig;

    // Factory function for creating item mutations with consistent handling
    const useCreateItemMutation = (mutationOptions: any) => {
        return useMutation({
            ...mutationOptions,
            onSuccess: () => {
                toast.success("Item used successfully");
                setOpen(false);
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
            },
            onError: (error: any) => {
                console.error("Failed to use item:", error);
                toast.error(error.message || "Failed to use item");
            },
        });
    };

    const useDeathNoteMutation = useCreateItemMutation(api.specialItems.useDeathNote.mutationOptions());
    const useLifeNoteMutation = useCreateItemMutation(api.specialItems.useLifeNote.mutationOptions());
    const useKompromatMutation = useCreateItemMutation(api.specialItems.useKompromat.mutationOptions());
    const useMegaphoneMutation = useCreateItemMutation(api.specialItems.useMegaphone.mutationOptions());

    const postUseUniqueItem = () => {
        if (item.name === HOSPITALISE_ITEM_NAME) {
            if (reason === "") {
                toast.error("You must enter an injury name!");
                return;
            }
            useDeathNoteMutation.mutate({
                userId: targetId,
                injuryName: reason,
                injuryType: injuryType,
            });
        } else if (item.name === REVIVE_ITEM_NAME) {
            if (!targetId) {
                toast.error("You must enter a target student ID!");
                return;
            }
            useLifeNoteMutation.mutate({
                userId: targetId,
            });
        } else if (item.name === JAIL_ITEM_NAME) {
            if (reason === "") {
                toast.error("You must enter a reason!");
                return;
            }
            if (!targetId) {
                toast.error("You must enter a target student ID!");
                return;
            }
            useKompromatMutation.mutate({
                userId: targetId,
                reason: reason,
            });
        } else if (item.name === MEGAPHONE_ITEM_NAME) {
            if (!reason || reason.length < 5) {
                toast.error("Enter a message with at least 5 characters!");
                return;
            }
            useMegaphoneMutation.mutate({
                message: reason,
            });
        } else {
            toast.error("Unknown item type!");
            return;
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        postUseUniqueItem();
    };

    return (
        <Modal
            showClose
            open={open}
            title={title}
            iconBackground="shadow-lg"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/JXozQjh.png`}
                    alt=""
                    className="mt-0.5 h-11 w-auto"
                />
            )}
            onOpenChange={setOpen}
        >
            <form className="mt-4 w-full max-w-lg" onSubmit={handleSubmit}>
                {item.name !== MEGAPHONE_ITEM_NAME && (
                    <div className="-mx-3 flex flex-wrap md:mb-2">
                        <div className="mb-6 w-full px-3 md:mb-0">
                            <label
                                className="mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                                htmlFor="studentid"
                            >
                                Target Student ID<span className="text-red-500"> *</span>
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <span className="inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200">
                                    #
                                </span>
                                <input
                                    type="number"
                                    name="studentid"
                                    min={1}
                                    id="studentid"
                                    className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                                    placeholder="0"
                                    onChange={(e) => setTargetId(Number.parseInt(e.target.value) || null)}
                                />
                            </div>
                        </div>
                    </div>
                )}
                {item.name !== REVIVE_ITEM_NAME && item.name !== MEGAPHONE_ITEM_NAME && (
                    <div className="-mx-3 flex flex-wrap md:mt-5">
                        <div className="mb-2 w-full px-3 md:mb-0">
                            <label
                                className="mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                                htmlFor="grid-first-name"
                            >
                                {item.name === HOSPITALISE_ITEM_NAME ? "Injury Name" : "Reason"}
                                <span className="text-red-500"> *</span>
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <input
                                    type="text"
                                    name="text"
                                    id="text"
                                    maxLength={100}
                                    className="block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                                    placeholder=""
                                    onChange={(e) => setReason(e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                )}

                {item.name === MEGAPHONE_ITEM_NAME && (
                    <>
                        <div className="-mt-2 text-center text-gray-200 text-lg">
                            <p className="text-custom-yellow">Broadcast a global message to all students</p>
                            <p className="text-red-400 text-xs">
                                Messages are monitored by Staff, inappropriate messages may result in punishment
                            </p>
                        </div>
                        <div className="-mx-3 flex flex-wrap md:mt-5">
                            <div className="mb-2 w-full px-3 md:mb-0">
                                <label
                                    className="mb-2 flex flex-row items-center text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                                    htmlFor="grid-first-name"
                                >
                                    Global Message
                                    <span className="text-red-500"> *</span>
                                    <small className="my-auto ml-4 text-gray-400">
                                        {"(Max length: 400 characters)"}
                                    </small>
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <textarea
                                        id="globalMessage"
                                        name="globalMessage"
                                        rows={10}
                                        maxLength={currentUser?.userType === "admin" ? 4000 : 400}
                                        className="block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                                        placeholder="Add a message.."
                                        value={reason}
                                        onChange={(e) => setReason(e.target.value)}
                                    />
                                </div>
                            </div>
                        </div>
                    </>
                )}

                {item.name === HOSPITALISE_ITEM_NAME && (
                    <div className="-mx-3 flex flex-wrap md:mt-5">
                        <div className="mb-4 flex w-full px-3 md:mb-0">
                            <fieldset className="mx-auto mt-1 flex w-3/4 flex-col gap-3 rounded-md border-2 border-gray-600 bg-slate-800 px-4 py-1 text-gray-200 text-stroke-sm md:p-4">
                                <legend className="text-stroke-sm!">
                                    Select an Injury Debuff <span className="text-red-500"> *</span>
                                </legend>

                                <div>
                                    <input
                                        type="radio"
                                        id="concussion"
                                        name="injury"
                                        value="concussion"
                                        checked={injuryType === "accuracy"}
                                        onChange={() => setInjuryType("accuracy")}
                                    />
                                    <label className="ml-2 inline-flex font-medium" htmlFor="concussion">
                                        Concussion{" "}
                                        <img
                                            src={statusEffects["concussion_injury"]?.icon}
                                            alt=""
                                            className="my-auto ml-2 h-5 w-auto"
                                        />
                                    </label>
                                    <p className="font-body font-semibold text-indigo-500 text-sm">
                                        Increased chance to miss attacks
                                    </p>
                                </div>

                                <div>
                                    <input
                                        type="radio"
                                        id="bleed"
                                        name="injury"
                                        value="bleed"
                                        checked={injuryType === "bleed"}
                                        onChange={() => setInjuryType("bleed")}
                                    />
                                    <label className="ml-2 inline-flex" htmlFor="bleed">
                                        Bleed
                                        <img
                                            src={statusEffects["bleeding_injury"]?.icon}
                                            alt=""
                                            className="my-auto ml-2 h-5 w-auto"
                                        />
                                    </label>
                                    <p className="font-body font-semibold text-indigo-500 text-sm">
                                        HP loss each combat turn
                                    </p>
                                </div>

                                <div>
                                    <input
                                        type="radio"
                                        id="fracture"
                                        name="injury"
                                        value="fracture"
                                        checked={injuryType === "damage"}
                                        onChange={() => setInjuryType("damage")}
                                    />
                                    <label className="ml-2 inline-flex font-medium" htmlFor="fracture">
                                        Fracture
                                        <img
                                            src={statusEffects["fracture_injury"]?.icon}
                                            alt=""
                                            className="my-auto ml-2 h-5 w-auto"
                                        />
                                    </label>
                                    <p className="font-body font-semibold text-indigo-500 text-sm">
                                        Reduced damage output
                                    </p>
                                </div>

                                <div>
                                    <input
                                        className=""
                                        type="radio"
                                        id="fatigue"
                                        name="injury"
                                        value="fatigue"
                                        checked={injuryType === "ability_lock"}
                                        onChange={() => setInjuryType("ability_lock")}
                                    />
                                    <label className="ml-2 inline-flex font-medium" htmlFor="fatigue">
                                        Fatigue
                                        <img
                                            src={statusEffects["fatigue_injury"]?.icon}
                                            alt=""
                                            className="my-auto ml-2 h-5 w-auto"
                                        />
                                    </label>
                                    <p className="font-body font-semibold text-indigo-500 text-sm">
                                        Can&apos;t use abilities
                                    </p>
                                </div>

                                <div>
                                    <input
                                        type="radio"
                                        id="muscle"
                                        name="injury"
                                        value="muscle"
                                        checked={injuryType === "defence"}
                                        onChange={() => setInjuryType("defence")}
                                    />
                                    <label className="ml-2 inline-flex font-medium" htmlFor="muscle">
                                        Muscle Injury
                                        <img
                                            src={statusEffects["muscle_injury"]?.icon}
                                            alt=""
                                            className="my-auto ml-2 h-5 w-auto"
                                        />
                                    </label>
                                    <p className="font-body font-semibold text-indigo-500 text-sm">Reduced defence</p>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                )}

                <div className="sm:mt-6">
                    <button
                        type="submit"
                        className="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-base text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm"
                    >
                        Use Item
                    </button>
                </div>
            </form>
        </Modal>
    );
}
