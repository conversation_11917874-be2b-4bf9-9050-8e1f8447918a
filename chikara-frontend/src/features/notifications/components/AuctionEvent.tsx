import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Flame } from "lucide-react";
import { Link } from "react-router-dom";
import { formatCurrency } from "@/utils/currencyHelpers";

interface AuctionEventDetails {
    quantity: number;
    itemName: string;
    buyerId?: number;
    listingComplete?: boolean;
    fundsBanked?: boolean;
    totalAmount?: number;
}

interface AuctionEventProps {
    details: AuctionEventDetails;
    read: boolean;
    type: "auction_item_sold" | "auction_item_expired";
}

function AuctionEvent({ details, read, type }: AuctionEventProps) {
    const { quantity, itemName } = details;
    const buyerId = details?.buyerId;
    const { data: user } = useGetUserInfo(buyerId, {
        enabled: !!buyerId,
    });

    if (type === "auction_item_expired") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    Your market listing of <span className="text-custom-yellow">{quantity}x</span>{" "}
                    <span className="text-custom-yellow">{itemName}</span> has expired.
                    <p> The item{quantity > 1 ? "s were " : " was "} returned to your inventory</p>
                </td>
            </>
        );
    }
    const { listingComplete, fundsBanked, totalAmount } = details;
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                {listingComplete ? "Your market listing of " : "Your market listing of "}
                <span className="text-custom-yellow">{quantity}x</span>{" "}
                <span className="text-custom-yellow">{itemName}</span> was bought by{" "}
                <Link className="text-blue-500" to={`/profile/${buyerId}`}>
                    {user?.username}
                </Link>{" "}
                for <span className="text-green-500">{formatCurrency(totalAmount!)}</span>.
                {fundsBanked && <p> The money was transferred to your bank account for a 15% fee.</p>}
            </td>
        </>
    );
}

export default AuctionEvent;
