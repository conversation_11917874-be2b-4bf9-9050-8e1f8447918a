import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Sparkles } from "lucide-react";
import { Link } from "react-router-dom";

interface CommentEventDetails {
    senderId: number;
    type?: string;
    suggestionId?: number;
    suggestionTitle?: string;
}

interface CommentEventProps {
    details: CommentEventDetails;
    read: boolean;
    currentUserId?: number;
}

interface UserData {
    username: string;
}

interface ProfileCommentEventProps {
    details: CommentEventDetails;
    read: boolean;
    currentUserId?: number;
    user?: UserData;
}

interface SuggestionCommentEventProps {
    details: CommentEventDetails;
    read: boolean;
    user?: UserData;
}

interface SuggestionReplyCommentEventProps {
    details: CommentEventDetails;
    read: boolean;
    user?: UserData;
}

const ProfileCommentEvent = ({ details, read, currentUserId, user }: ProfileCommentEventProps) => {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <Link to={`/profile/${details.senderId}`} className="mr-1 font-semibold text-blue-600 text-stroke-sm">
                    {user?.username}
                </Link>
                left a new comment on your profile.{" "}
                <Link className="ml-1 inline text-sky-500 text-stroke-0 underline" to={`/profile/${currentUserId}`}>
                    View Comment
                </Link>
            </td>
        </>
    );
};

const SuggestionCommentEvent = ({ details, read, user }: SuggestionCommentEventProps) => {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <Link to={`/profile/${details.senderId}`} className="mr-1 font-semibold text-blue-600 text-stroke-sm">
                    {user?.username}
                </Link>
                left a new comment on your suggestion.{" "}
                <Link
                    className="ml-1 inline text-sky-500 text-stroke-0 underline"
                    to={`/suggestions?id=${details?.suggestionId}`}
                >
                    View Comment
                </Link>
            </td>
        </>
    );
};

const SuggestionReplyCommentEvent = ({ details, read, user }: SuggestionReplyCommentEventProps) => {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <Link to={`/profile/${details.senderId}`} className="mr-1 font-semibold text-blue-600 text-stroke-sm">
                    {user?.username}
                </Link>
                left a new comment in the suggestion '{details?.suggestionTitle}'.{" "}
                <Link
                    className="ml-1 inline text-sky-500 text-stroke-0 underline"
                    to={`/suggestions?id=${details?.suggestionId}`}
                >
                    View Thread
                </Link>
            </td>
        </>
    );
};

function CommentEvent({ details, read, currentUserId }: CommentEventProps) {
    const { data: user } = useGetUserInfo(details.senderId);
    if (!details?.type) {
        return <ProfileCommentEvent details={details} read={read} currentUserId={currentUserId} user={user} />;
    }
    if (details?.type === "suggestion") {
        return <SuggestionCommentEvent details={details} read={read} user={user} />;
    }
    if (details?.type === "suggestion_reply") {
        return <SuggestionReplyCommentEvent details={details} read={read} user={user} />;
    }
    return <ProfileCommentEvent details={details} read={read} currentUserId={currentUserId} user={user} />;
}

export default CommentEvent;
