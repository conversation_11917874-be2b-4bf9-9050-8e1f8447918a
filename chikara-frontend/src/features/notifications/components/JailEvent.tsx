import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import toDate from "@/helpers/toDate";
import { format } from "date-fns";
import { AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";

interface JailEventDetails {
    attacked?: number;
    jailedUntil: string;
    reason?: string;
}

interface JailEventProps {
    details: JailEventDetails;
    read: boolean;
}

function JailEvent({ details, read }: JailEventProps) {
    const { data: attackedStudent } = useGetUserInfo(details?.attacked);

    const releaseTime = format(toDate(details?.jailedUntil), "p");
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <AlertTriangle className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                {details?.reason === "Assault" && (
                    <>
                        <p className="inline-block">
                            You were Jailed until{" "}
                            <span className="text-indigo-800 dark:text-indigo-400">{releaseTime}</span> for attacking
                        </p>
                        <Link className="inline text-blue-600" to={`/profile/${details?.attacked}`}>
                            {" "}
                            {attackedStudent?.username}
                        </Link>
                    </>
                )}
            </td>
        </>
    );
}

export default JailEvent;
