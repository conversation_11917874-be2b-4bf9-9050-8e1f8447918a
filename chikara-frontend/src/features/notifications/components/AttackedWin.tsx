import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Flame } from "lucide-react";
import { Link } from "react-router-dom";

interface AttackedWinEventDetails {
    attacked: number;
    result?: string;
}

interface AttackedWinEventProps {
    details: AttackedWinEventDetails;
    read: boolean;
}

function AttackedWin({ details, read }: AttackedWinEventProps) {
    const { data: user } = useGetUserInfo(details.attacked);

    return (
        <>
            <td className={`py-4 pr-3 pl-4 text-xs sm:pl-6 md:text-sm ${!read && "bg-blue-50 dark:bg-blue-900"}`}>
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={` px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-white ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <p>
                    You were attacked by{" "}
                    {details.attacked === 0 ? (
                        <span className="text-black dark:text-slate-200 dark:text-stroke-sm">Anonymous</span>
                    ) : (
                        <Link className="text-blue-600" to={`/profile/${details.attacked}`}>
                            {user?.username}
                        </Link>
                    )}{" "}
                    {details.result === "flee" ? "but they ran away mid battle!" : "but they were no match for you!"}
                </p>
            </td>
        </>
    );
}

export default AttackedWin;
