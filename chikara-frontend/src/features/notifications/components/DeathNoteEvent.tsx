import { Flame } from "lucide-react";

interface DeathNoteEventDetails {
    injuryName?: string;
}

interface DeathNoteEventProps {
    details: DeathNoteEventDetails;
    read: boolean;
}

function DeathNoteEvent({ details, read }: DeathNoteEventProps) {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                An unknown player used a Death Book on you, you received a Severe Injury:{" "}
                <span className="text-red-500">{details?.injuryName}</span>!
            </td>
        </>
    );
}

export default DeathNoteEvent;
