import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { useEffect } from "react";
import AdminActionEvent from "./AdminActionEvent";
import AttackedWin from "./AttackedWin";
import AuctionEvent from "./AuctionEvent";
import BankTransferEvent from "./BankTransferEvent";
import BountyEvent from "./BountyEvent";
import CommentEvent from "./CommentEvent";
import DeathNoteEvent from "./DeathNoteEvent";
import FightWinEvent from "./FightWinEvent";
import GangEvent from "./GangEvent";
import HospitalisedEvent from "./HospitalisedEvent";
import JailEvent from "./JailEvent";
import JobEvent from "./JobEvent";
import LevelupEvent from "./LevelupEvent";
import LifeNoteEvent from "./LifeNoteEvent";
import MissionEvent from "./MissionEvent";
import NewPatchNotes from "./NewPatchNotes";

// Define notification type based on backend NotificationTypes
export type NotificationType =
    | "levelup"
    | "hospitalised"
    | "jail"
    | "job_paid"
    | "transfer_received"
    | "transfer_sent"
    | "fight_win"
    | "fight_win_attacked"
    | "new_patch_notes"
    | "bounty_claimed"
    | "bounty_placed"
    | "profile_comment"
    | "death_noted"
    | "life_noted"
    | "mission_completed"
    | "admin_action"
    | "auction_item_sold"
    | "auction_item_expired"
    | "gang_invite"
    | "gang_invite_request"
    | "gang_kicked"
    | "gang_rank_change"
    | "temporary_notification";

// Define notification object structure
export interface Notification {
    id: number;
    notificationType: NotificationType;
    details: any; // This will be parsed from JSON string
    read: boolean;
    createdAt: string;
    userId: number;
}

interface RenderEventProps {
    type: NotificationType;
    details: any;
    read: boolean;
    currentUserId?: number;
}

const renderEvent = ({ type, details, read, currentUserId }: RenderEventProps) => {
    switch (type) {
        case "levelup":
            return <LevelupEvent details={details} read={read} />;
        case "hospitalised":
            return <HospitalisedEvent details={details} read={read} />;
        case "jail":
            return <JailEvent details={details} read={read} />;
        case "job_paid":
            return <JobEvent details={details} read={read} />;
        case "transfer_received":
        case "transfer_sent":
            return <BankTransferEvent details={details} type={type} read={read} />;
        case "fight_win":
            return <FightWinEvent details={details} read={read} />;
        case "fight_win_attacked":
            return <AttackedWin details={details} read={read} />;
        case "new_patch_notes":
            return <NewPatchNotes details={details} read={read} />;
        case "bounty_claimed":
        case "bounty_placed":
            return <BountyEvent details={details} read={read} type={type} />;
        case "profile_comment":
            return <CommentEvent details={details} read={read} currentUserId={currentUserId} />;
        case "death_noted":
            return <DeathNoteEvent details={details} read={read} />;
        case "life_noted":
            return <LifeNoteEvent details={details} read={read} />;
        case "mission_completed":
            return <MissionEvent details={details} read={read} />;
        case "admin_action":
            return <AdminActionEvent details={details} read={read} />;
        case "auction_item_sold":
            return <AuctionEvent details={details} read={read} type={type} />;
        case "auction_item_expired":
            return <AuctionEvent details={details} read={read} type={type} />;
        case "gang_invite":
        case "gang_invite_request":
        case "gang_kicked":
        case "gang_rank_change":
            return <GangEvent details={details} read={read} type={type} />;
        default:
            return null;
    }
};

interface EventManagerProps {
    singleEvent: Notification;
}

function EventManager({ singleEvent }: EventManagerProps) {
    const queryClient = useQueryClient();

    const markReadMutation = useMutation(
        api.notifications.markRead.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.notifications.getUnreadCount.key(),
                });
            },
            onError: (error) => {
                console.log("Error marking notification as read:", error);
            },
        })
    );

    useEffect(() => {
        if (singleEvent.read === false) {
            markReadMutation.mutate({ notificationId: singleEvent.id });
        }
    }, [singleEvent.id, singleEvent.read, markReadMutation.mutate]);

    return (
        <tr className="border-gray-200 border-t font-medium font-body text-gray-900 text-stroke-sm tracking-wide dark:border-gray-600 dark:text-white">
            {renderEvent({
                type: singleEvent.notificationType,
                details: singleEvent.details,
                read: singleEvent.read,
                currentUserId: singleEvent.userId,
            })}
            <td
                className={`relative whitespace-nowrap py-4 pr-4 pl-3 text-right font-medium text-stroke-s-sm text-xs sm:pr-6 md:text-sm dark:text-gray-300 ${
                    singleEvent?.read === false && "bg-blue-50 text-blue-600 dark:bg-blue-900"
                }`}
            >
                {format(new Date(singleEvent?.createdAt), "p")}
            </td>
        </tr>
    );
}

export default EventManager;
