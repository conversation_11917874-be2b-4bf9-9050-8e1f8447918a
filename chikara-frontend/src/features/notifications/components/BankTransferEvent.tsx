import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Banknote } from "lucide-react";
import { Link } from "react-router-dom";
import { formatCurrency } from "@/utils/currencyHelpers";

interface BankTransferEventDetails {
    user: number;
    amount: number;
}

interface BankTransferEventProps {
    details: BankTransferEventDetails;
    type: "transfer_received" | "transfer_sent";
    read: boolean;
}

function BankTransferEvent({ details, type, read }: BankTransferEventProps) {
    const { data: user } = useGetUserInfo(details.user);

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Banknote className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <p>
                    You {type === "transfer_received" ? "received" : "sent"}{" "}
                    <span className="text-indigo-800 dark:text-indigo-400">{formatCurrency(details.amount)}</span>{" "}
                    {type === "transfer_received" ? "from" : "to"}
                    <Link className="inline text-blue-600" to={`/profile/${details.user}`}>
                        {" "}
                        {user?.username}
                    </Link>{" "}
                    via bank transfer
                </p>
            </td>
        </>
    );
}

export default BankTransferEvent;
