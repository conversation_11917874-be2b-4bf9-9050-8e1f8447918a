import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { <PERSON>, Sparkles } from "lucide-react";
import { Link } from "react-router-dom";

const gangRanks: Record<number, string> = {
    6: "Leader",
    5: "Lieutenant",
    4: "Officer",
    3: "Thug",
    2: "Member",
    1: "Rookie",
};

interface GangEventDetails {
    senderId?: number;
    gangName?: string;
    manualPayout?: boolean;
    amount?: number;
    gangRank?: number;
    totalAmount?: number;
    payoutShare?: number;
    oldRank?: number;
    newRank?: number;
}

interface GangEventProps {
    details: GangEventDetails;
    read: boolean;
    type: "gang_invite" | "gang_invite_request" | "gang_kicked" | "gang_rank_change" | "temporary_notification";
}

function GangEvent({ details, read, type }: GangEventProps) {
    const senderId = details?.senderId;
    const { data: user } = useGetUserInfo(senderId, {
        enabled: !!senderId,
    });

    if (type === "temporary_notification") {
        if (details?.manualPayout) {
            // TODO - remove this when we have a better way to handle this
            return (
                <>
                    <td
                        className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                            !read && "bg-blue-50 dark:bg-blue-900"
                        }`}
                    >
                        <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                    </td>
                    <td
                        className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                    >
                        <p>Your Gang resources were converted into Gang Creds and distributed to your Gang!</p>

                        <p className="mt-1 text-green-500 text-stroke-sm">You received {details?.amount} Gang Creds!</p>
                    </td>
                </>
            );
        }
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    <p>
                        Your gang ranked <span className="text-custom-yellow">#{details?.gangRank}</span> this week and
                        earned <span className="text-custom-yellow">{details?.totalAmount}</span> total creds!
                    </p>
                    Your share:{""}
                    <span className="ml-1 text-blue-500">{details?.payoutShare?.toFixed(0)}%</span>
                    <p className="mt-1 text-green-500 text-stroke-sm">You received {details?.amount} Gang Creds!</p>
                </td>
            </>
        );
    }

    if (type === "gang_invite") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    {user?.username} has invited you to join the Gang{" "}
                    <span className="text-custom-yellow">{details?.gangName}</span>
                    <Link to={`/gang`}>
                        <p className="mt-1 text-blue-500 text-stroke-sm underline underline-offset-2">
                            View Gang Invites
                        </p>
                    </Link>
                </td>
            </>
        );
    }

    if (type === "gang_kicked") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                        !read && "bg-blue-50 dark:bg-blue-900"
                    }`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    You were kicked from the Gang <span className="text-custom-yellow">{details?.gangName}</span>
                </td>
            </>
        );
    }

    if (type === "gang_rank_change") {
        return (
            <>
                <td
                    className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
                </td>
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    You were{" "}
                    <span className={details?.oldRank! < details?.newRank! ? "text-green-500" : "text-red-500"}>
                        {details?.oldRank! < details?.newRank! ? "promoted" : "demoted"}
                    </span>{" "}
                    to the rank of <span className="text-blue-400">{gangRanks[details?.newRank!]}</span> in your
                    Gang{" "}
                </td>
            </>
        );
    }

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <span className="text-custom-yellow">{user?.username}</span> has requested to join your Gang{" "}
                <Link to={`/gang`}>
                    <p className="mt-1 text-blue-500 text-stroke-sm underline underline-offset-2">
                        View Gang Applications
                    </p>
                </Link>
            </td>
        </>
    );
}

export default GangEvent;
