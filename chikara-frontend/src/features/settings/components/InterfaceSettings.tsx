import ToggleSwitch from "@/components/ToggleSwitch";
import { User } from "@/types/user";
import { usePersistStore } from "../../../app/store/stores";

interface InterfaceSettingsProps {
    currentUser?: User;
}

export default function InterfaceSettings({ currentUser }: InterfaceSettingsProps) {
    const {
        hideGlobalChat,
        setHideGlobalChat,
        twelveHrClock,
        setTwelveHrClock,
        colourTheme,
        setColourTheme,
        keyboardShortcutsEnabled,
        setKeyboardShortcutsEnabled,
        zombieEffectsDisabled,
        setZombieEffectsDisabled,
    } = usePersistStore();

    return (
        <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600">
            <div className="divide-y divide-gray-200 dark:divide-gray-600">
                <div className="px-4 sm:px-6">
                    <div>
                        <h2 className="mt-6 font-medium text-gray-900 text-lg leading-6 dark:text-gray-200">
                            Interface
                        </h2>
                    </div>
                    <ul className="mt-2 divide-y divide-gray-200 dark:divide-gray-600">
                        <ToggleSwitch
                            label="Enable Combat Keyboard Shortcuts"
                            description="Enable the use of keyboard shortcuts while in battle."
                            className="pt-4"
                            value={keyboardShortcutsEnabled}
                            onChange={setKeyboardShortcutsEnabled}
                        />

                        <ToggleSwitch
                            label="Hide Global Chat"
                            description="Disable the use of the sidebar global chat."
                            className="pt-4"
                            value={hideGlobalChat}
                            onChange={setHideGlobalChat}
                        />

                        <ToggleSwitch
                            label="12hr Clock Format"
                            description="Enable the 12 hour clock time format."
                            className="pt-4"
                            value={twelveHrClock}
                            onChange={setTwelveHrClock}
                        />

                        <ToggleSwitch
                            label="Disable Zombie Effects"
                            description="Disable the visual screen effects when you are a zombie."
                            className="pt-4"
                            value={zombieEffectsDisabled}
                            onChange={setZombieEffectsDisabled}
                        />

                        {/* <ToggleSwitch
              label=""
              description=""
              className="pt-4"
              value={colourTheme}
              onChange={setColourTheme}
            /> */}
                    </ul>
                </div>
            </div>
        </form>
    );
}
