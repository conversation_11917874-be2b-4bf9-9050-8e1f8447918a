import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { User } from "@/types/user";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { changeEmail } from "../../../lib/auth-client";

interface EmailSettingsProps {
    currentUser?: User;
}

export default function EmailSettings({ currentUser }: EmailSettingsProps) {
    const { data: fetchedUser, refetch } = useFetchCurrentUser();

    // Use passed currentUser or fallback to fetched user
    const user = currentUser || fetchedUser;

    const [email, setEmail] = useState<string>("");
    const [confirmEmail, setConfirmEmail] = useState<string>("");
    const [password, setPassword] = useState<string>("");

    const updateEmail = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
        e.preventDefault();
        if (email !== confirmEmail) {
            toast.error("The emails do not match!");
            return;
        }

        try {
            const { data, error } = await changeEmail({
                newEmail: email,
            });

            if (data && !error) {
                toast.success("Changes Saved successfully!");
                setEmail("");
                setConfirmEmail("");
                setPassword("");
                refetch();
            } else {
                if (error?.message) {
                    toast.error(error.message);
                } else {
                    toast.error("Error saving changes!");
                }
                console.error(error);
            }
        } catch (error) {
            toast.error("An unexpected error occurred!");
            console.error(error);
        }
    };

    return (
        <>
            <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600" onSubmit={updateEmail}>
                <div className="px-4 py-2 sm:p-6 md:py-6 lg:pb-8">
                    <div>
                        <h2 className="font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200">
                            Email
                        </h2>
                    </div>
                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    Current Email Address
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        disabled
                                        value={user?.email || ""}
                                        type="text"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 bg-slate-200 opacity-75 sm:text-sm dark:border-gray-600 dark:bg-gray-600 dark:text-white"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    New Email Address
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        value={email}
                                        type="email"
                                        name="email"
                                        autoComplete="email"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setEmail(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="mt-6 flex flex-col lg:flex-row">
                        <div className="grow space-y-6">
                            <div>
                                <label
                                    htmlFor="username"
                                    className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                >
                                    Confirm Email Address
                                </label>
                                <div className="mt-1 flex rounded-md shadow-xs">
                                    <input
                                        value={confirmEmail}
                                        type="email"
                                        name="confirmEmail"
                                        autoComplete="email"
                                        className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setConfirmEmail(e.target.value);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* <div className="mt-6 flex flex-col lg:flex-row">
            <div className="grow space-y-6">
              <div>
                <label
                  htmlFor="username"
                  className="block uppercase tracking-wide text-gray-700 text-xs font-bold mb-2 dark:text-gray-300 dark:font-normal"
                >
                  Password
                </label>
                <div className="mt-1 rounded-md shadow-xs flex">
                  <input
                    onChange={(e) => {
                      setPassword(e.target.value);
                    }}
                    value={password}
                    type="password"
                    name="password"
                    autoComplete="password"
                    className="focus:ring-light-blue-500 focus:border-light-blue-500 grow block w-full min-w-0 rounded-md sm:text-sm border-gray-300 dark:border-gray-600 dark:text-gray-200 dark:bg-slate-900"
                  />
                </div>
              </div>
            </div>
          </div> */}
                </div>

                <div className="mt-4 flex justify-end p-4 sm:px-6">
                    <button
                        type="submit"
                        className="ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                    >
                        Save
                    </button>
                </div>
            </form>
        </>
    );
}
