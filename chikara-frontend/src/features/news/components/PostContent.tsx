import { DisplayItem } from "@/components/DisplayItem";
import ShinyButton from "@/components/ui/shiny-button";
import clsx from "clsx";
import reactStringReplace from "react-string-replace";

interface PostContentItem {
    id: number;
    text: string;
    item: {
        id: number;
        name: string;
        image?: string | null;
    };
}

interface PostContentSection {
    heading?: string;
    body?: string;
    image?: string;
    textList?: string[];
    list?: string[];
    itemList?: PostContentItem[];
    lowerBody?: string;
    lowerList?: string[];
    footerText?: string;
}

interface SelectedPost {
    id: number;
    title?: string;
    version?: string;
    image?: string;
    imageClassLarge?: string;
    description?: string;
    subDescription?: string;
    content?: PostContentSection[];
}

interface HighlightedTextProps {
    text: string;
    className?: string;
    bg?: string;
    textColor?: string;
    link?: boolean;
}

const HighlightedText = ({
    text,
    className = "px-1 mx-0.5",
    bg = "bg-custom-yellow",
    textColor = "text-black",
}: HighlightedTextProps) => {
    const replacedText = reactStringReplace(text, /\{\{(.*?)\}\}/g, (match, i) => (
        <span key={i + text} className={clsx("rounded-md font-extrabold text-stroke-0", textColor, bg, className)}>
            {match}
        </span>
    ));

    return <span>{replacedText}</span>;
};

interface PostContentProps {
    selectedPost: SelectedPost | null | undefined;
}

const PostContent = ({ selectedPost }: PostContentProps) => {
    return (
        <div className="modalHeaderBackground vignette-sm flex flex-col gap-4 border-slate-700/50 bg-slate-900/95 bg-blend-overlay lg:rounded-2xl lg:border">
            <div className="relative size-full">
                {selectedPost?.version && (
                    <div className="absolute right-0.5 bottom-0.5 z-10 ">
                        <ShinyButton rounded={false} text={selectedPost?.version} />
                    </div>
                )}

                <img
                    src={selectedPost?.image}
                    alt=""
                    className={clsx(
                        "mx-auto size-full rounded-md object-cover object-center group-hover:brightness-105 lg:rounded-2xl 2xl:h-80",
                        selectedPost?.imageClassLarge
                    )}
                />
            </div>
            <div className="p-4 lg:px-16 lg:pb-8">
                <p className="mb-4 text-center font-bold font-display text-4xl text-custom-yellow uppercase tracking-wide ">
                    {selectedPost?.title}
                </p>
                {(selectedPost?.description || selectedPost?.subDescription) && (
                    <div className="relative">
                        <p className="prose prose-slate dark:prose-dark prose-a:relative prose-a:z-10 font-body font-semibold dark:text-slate-300">
                            <HighlightedText text={selectedPost?.description || ""} />
                        </p>
                        {selectedPost?.subDescription && (
                            <p className="prose mt-4 rounded-lg bg-black/15 p-1 font-medium font-body dark:text-slate-300">
                                {selectedPost?.subDescription}
                            </p>
                        )}
                    </div>
                )}

                {selectedPost?.content && (
                    <div className="mt-4 flex flex-col gap-4 font-body">
                        {selectedPost.content.map((content, i) => (
                            <div key={i} className="flex flex-col">
                                {content.heading && (
                                    <p className="mt-2 w-full rounded-t-md border-gray-900/50 border-x border-t bg-black/25 px-2 py-1 text-center font-semibold text-black text-xl uppercase dark:text-custom-yellow">
                                        {content.heading}
                                    </p>
                                )}
                                <div
                                    className={clsx(
                                        "rounded-b-md border-gray-600/50 border-x border-b bg-blue-700/25 px-3 py-2",
                                        !content.heading && "rounded-md pt-3"
                                    )}
                                >
                                    {content.body && (
                                        <p className="mb-4 font-medium text-base text-slate-900 dark:text-slate-200">
                                            <HighlightedText
                                                className=""
                                                textColor="text-sky-300 text-stroke-s-sm font-medium"
                                                bg=""
                                                text={content.body}
                                            />
                                        </p>
                                    )}

                                    {content.image && (
                                        <img
                                            src={content.image}
                                            alt=""
                                            className="my-6 h-auto max-h-120 w-full scale-110 rounded-md object-contain lg:my-0 lg:scale-100"
                                        />
                                    )}

                                    {content.textList?.map((item) => (
                                        <p
                                            key={item}
                                            className="prose my-2 font-display text-slate-900 text-sm md:text-base dark:text-slate-200"
                                        >
                                            <HighlightedText
                                                className=""
                                                textColor="text-sky-300 text-stroke-s-sm font-medium"
                                                bg=""
                                                text={item}
                                            />
                                        </p>
                                    ))}

                                    <ul className="list-inside list-disc space-y-2">
                                        {content.list?.map((item) => (
                                            <li
                                                key={item}
                                                className="text-slate-900 text-sm md:text-base dark:text-slate-200"
                                            >
                                                <HighlightedText
                                                    className=""
                                                    textColor="text-red-400 font-bold text-base text-stroke-s-sm font-medium font-display"
                                                    bg="bg-black/25 px-1 rounded-lg"
                                                    text={item}
                                                />
                                            </li>
                                        ))}
                                    </ul>
                                    <ul className="flex list-inside flex-col gap-4">
                                        {content.itemList?.map((item) => (
                                            <li
                                                key={item.id}
                                                className="flex items-center gap-2 text-base text-slate-900 dark:text-slate-200"
                                            >
                                                <DisplayItem item={item.item} height="h-10!" className="inline-block" />
                                                <div className="flex-col items-center gap-2 text-sm lg:flex-row lg:text-base">
                                                    {" "}
                                                    <p className="font-semibold text-yellow-400">{item.item.name} - </p>
                                                    {item.text}
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                    {content.lowerBody && (
                                        <p className="mt-4 font-medium text-base text-slate-900 dark:text-slate-200">
                                            <HighlightedText
                                                link
                                                className=""
                                                textColor="text-sky-300 text-stroke-s-sm font-medium"
                                                bg=""
                                                text={content.lowerBody}
                                            />
                                        </p>
                                    )}
                                    {content.lowerList && (
                                        <ul className="my-2 list-inside list-disc space-y-2">
                                            {content.lowerList.map((item) => (
                                                <li
                                                    key={item}
                                                    className="text-slate-900 text-sm md:text-base dark:text-slate-200"
                                                >
                                                    {item}
                                                </li>
                                            ))}
                                        </ul>
                                    )}
                                    {content.footerText && (
                                        <p className="mt-3 font-semibold text-blue-200 text-sm md:text-base">
                                            {content.footerText}
                                        </p>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PostContent;
