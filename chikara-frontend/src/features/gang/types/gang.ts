import type { ChatRoom } from "@/features/chat/types/chat";
import type { User } from "@/types/user";

// Gang type definition
export interface Gang {
    id: number;
    name: string;
    about: string;
    avatar: string | null;
    treasury_balance: number;
    hideout_level: number;
    materialsResource: number;
    essenceResource: number;
    dailyEssenceGained: number;
    toolsResource: number;
    techResource: number;
    weeklyRespect: number;
    totalRespect: number;
    gangMOTD: string | null;
    createdAt: string;
    updatedAt: string;
    ownerId: number;
    gang_member?: GangMember[];
    chat_room?: ChatRoom;
    currentUserRank?: number;
    owner?: User;
}

export interface GangMember {
    id: number;
    rank: number;
    weeklyMaterials: number;
    weeklyEssence: number;
    weeklyTools: number;
    user?: User;
}

export interface GangInvite {
    id: number;
    inviteType: "invite" | "inviteRequest";
    sender: User;
    senderId: number;
    receiver?: User;
}

export interface GangLog {
    id: number;
    action: string;
    info?: string;
    createdAt: string;
    gangMemberId?: number;
    secondPartyId?: number;
}
