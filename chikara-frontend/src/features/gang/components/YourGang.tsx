import Button from "@/components/Buttons/Button";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import LoadingState from "@/components/LoadingState";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import GangBanner from "./GangBanner";
import GangChatroom from "./GangChatroom";
import GangHideout from "./GangHideout";
import GangInfo from "./GangInfo";
import GangInviteModal from "./GangInviteModal";
import GangMemberModal from "./GangMemberModal";
import IconButton from "@/components/Buttons/IconButton";
import GangShop from "./GangShop";
import type { User } from "@/types/user";
import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "../types/gang";

interface YourGangProps {
    currentUser: User;
}

interface MemberListProps {
    currentGang: Gang | undefined;
    currentUserId: number;
}

interface TabsProps {
    currentTab: string;
    setCurrentTab: (tab: string) => void;
    hasChatroom?: any;
}

interface Tab {
    name: string;
    text: string;
    disabled?: boolean;
    href?: string;
}

const gangRanks: Record<number, string> = {
    6: "Leader",
    5: "Lieutenant",
    4: "Officer",
    3: "Thug",
    2: "Member",
    1: "Rookie",
};

export default function YourGang({ currentUser }: YourGangProps) {
    const [searchParams] = useSearchParams();
    const gangParamId = searchParams.get("id");

    const [currentTab, setCurrentTab] = useState("/members");

    // For admin viewing specific gang, use getGangInfo with gangId
    // For regular users, use getCurrentGang
    const { data: currentGang, isLoading } = useQuery(
        currentUser?.userType === "admin" && gangParamId
            ? api.gang.getGangInfo.queryOptions({ input: { gangId: parseInt(gangParamId) } })
            : api.gang.getCurrentGang.queryOptions()
    );

    return (
        <div className="w-full md:mx-auto md:max-w-4xl">
            <LoadingState isAbsolute size={16} isLoading={isLoading}>
                <div className="border-2 border-black bg-indigo-800 px-0.5 pt-1 pb-2">
                    <GangBanner gang={currentGang} />
                    {currentGang?.gangMOTD && (
                        <div className="mt-1 rounded-lg border-2 border-indigo-600 bg-slate-900 px-4 py-2">
                            <p className="text-center text-gray-300 text-xs uppercase">Message of the Day</p>
                            <p className="text-center text-blue-400 text-sm">{currentGang?.gangMOTD}</p>
                        </div>
                    )}
                </div>
                <Tabs currentTab={currentTab} setCurrentTab={setCurrentTab} hasChatroom={currentGang?.chat_room} />
                {currentTab === "/members" ? (
                    <MemberList currentGang={currentGang} currentUserId={currentUser.id} />
                ) : null}
                {currentTab === "/hideout" ? <GangHideout gang={currentGang} currentUser={currentUser} /> : null}
                {currentTab === "/chat" ? <GangChatroom gang={currentGang} currentUserId={currentUser.id} /> : null}
                {currentTab === "/info" ? <GangInfo currentGang={currentGang} currentUserId={currentUser.id} /> : null}
                {currentTab === "/shop" ? <GangShop currentGang={currentGang} currentUser={currentUser} /> : null}
            </LoadingState>
        </div>
    );
}

const MemberList = ({ currentGang, currentUserId }: MemberListProps) => {
    const [openInviteModal, setOpenInviteModal] = useState(false);
    const [openMemberModal, setOpenMemberModal] = useState(false);
    const [selectedMember, setSelectedMember] = useState<GangMember | null>(null);

    const getWeeklyContributionTotal = (member: GangMember) => {
        return member.weeklyMaterials + member.weeklyEssence * 3 + member.weeklyTools * 2;
    };

    const gangMembers = currentGang?.gang_member;
    const sortedMembers = gangMembers
        ?.slice()
        .sort((a, b) => getWeeklyContributionTotal(b) - getWeeklyContributionTotal(a));

    const handleOpenMemberModal = (member: GangMember) => {
        if (member?.user?.id === currentUserId) return;
        setSelectedMember(member);
        setOpenMemberModal(true);
    };

    return (
        <div className="flex flex-col gap-2 overflow-y-auto p-2">
            <GangInviteModal open={openInviteModal} setOpen={setOpenInviteModal} />
            <GangMemberModal
                open={openMemberModal}
                setOpen={setOpenMemberModal}
                member={selectedMember}
                setMember={setSelectedMember}
                currentUserRank={currentGang?.currentUserRank}
            />
            <div className="flex gap-2">
                <Link to="/gang/leaderboards">
                    <IconButton
                        icon={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nTBe65B.png`}
                        iconClassName="w-auto! p-1 h-8!"
                    />
                </Link>
                <div className="ml-auto flex gap-2">
                    <Link to="/ganglist">
                        <Button className="w-fit!">Gangs List</Button>
                    </Link>
                    <Button className="w-fit!" onClick={() => setOpenInviteModal(true)}>
                        Invite Members
                    </Button>
                </div>
            </div>

            {sortedMembers?.map((member, i) => (
                <div key={member.id}>
                    <div
                        className={cn(
                            "flex h-14 cursor-pointer items-center rounded-lg border-2 bg-linear-to-r p-1",
                            member.user.id === currentUserId
                                ? "border-custom-yellow from-yellow-500 to-yellow-800 text-white hover:border-yellow-400"
                                : "border-blue-600 from-blue-700 to-blue-900 text-custom-yellow hover:border-blue-400"
                        )}
                        onClick={() => handleOpenMemberModal(member)}
                    >
                        <div className="mr-2 ml-1 rounded-full bg-black/35 px-2.5 py-0.5 text-center">
                            <p>{i + 1}</p>
                        </div>
                        <div className="mr-2 size-10 shrink-0">
                            <DisplayAvatar src={member.user} />
                        </div>
                        <div className="flex flex-1 flex-col text-left">
                            <p className="font-body font-semibold text-lg ">{member.user.username}</p>
                            <p className="mb-2 text-red-400 text-sm leading-none">{gangRanks[member.rank]}</p>
                        </div>
                        <div className="mr-2 flex gap-2 text-stroke-sm">
                            <img
                                className="h-8 w-auto"
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/mazZ4Es.png`}
                                alt=""
                            />
                            <p className="my-auto text-lg">{getWeeklyContributionTotal(member)}</p>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
};

const Tabs = ({ currentTab, setCurrentTab, hasChatroom }: TabsProps) => {
    const tabs: Tab[] = [
        {
            name: "/members",
            text: "Members",
        },
        {
            name: "/hideout",
            text: "Hideout",
        },
        {
            name: "/chat",
            text: "Chat",
            disabled: !hasChatroom,
        },
        {
            name: "/info",
            text: "Info",
        },
        {
            name: "/shop",
            text: "Shop",
        },
    ];
    return (
        <div className="mt-0.5 block border-gray-600 border-t">
            <nav className="relative z-0 flex divide-x divide-gray-700 shadow-sm" aria-label="Tabs">
                {tabs.map((tab, tabIdx) => (
                    <a
                        key={tab.name}
                        href={tab.href}
                        disabled={tab.disabled}
                        aria-current={currentTab === tab.name ? "page" : undefined}
                        className={cn(
                            currentTab === tab.name ? "text-gray-900" : "text-gray-500 hover:text-gray-700",
                            tabIdx === tabs.length - 1 ? "md:rounded-r-lg" : "",
                            tabIdx === 0 ? "md:rounded-l-lg" : "",
                            tab.disabled ? "text-gray-500! pointer-events-none opacity-50" : "",
                            "group relative min-w-0 flex-1 cursor-pointer overflow-hidden bg-white px-4 py-3 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 md:py-4 md:text-base dark:bg-gray-800 dark:text-slate-200"
                        )}
                        onClick={() => (tab.disabled ? null : setCurrentTab(tab.name))}
                    >
                        <span>{tab.text}</span>
                        <span
                            aria-hidden="true"
                            className={cn(
                                currentTab === tab.name ? "bg-indigo-500" : "bg-transparent",
                                "absolute inset-x-0 bottom-0 h-0.5"
                            )}
                        />
                    </a>
                ))}
            </nav>
        </div>
    );
};
