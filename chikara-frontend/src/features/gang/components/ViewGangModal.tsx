import { DisplayAvatar } from "@/components/DisplayAvatar";
import { Modal } from "@/components/Modal/Modal";
import { format, parseISO } from "date-fns";
import { Check } from "lucide-react";
import { Link } from "react-router-dom";
import useRequestGangInvite from "../api/useRequestGangInvite";
import GangBanner from "./GangBanner";
import type { Gang } from "../types/gang";
import type { User } from "@/types/user";

interface ViewGangModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    selectedGang: Gang | null;
    setSelectedGang: (gang: GangWithOwner | null) => void;
    currentUser: User | null;
    hideInviteButton?: boolean;
}

export default function ViewGangModal({
    open,
    setOpen,
    selectedGang,
    setSelectedGang,
    currentUser,
    hideInviteButton,
}: ViewGangModalProps) {
    const { requestGangInvite } = useRequestGangInvite(setOpen);

    const handleSubmit = () => {
        requestGangInvite(selectedGang?.id);
    };

    const formatDate = (date: string) => {
        if (!date) return null;
        const parseDate = parseISO(date);
        const formattedDate = format(parseDate, "dd/MM/y");
        return formattedDate;
    };

    if (!selectedGang) return null;

    const handleClose = (change: boolean) => {
        if (!change) {
            setSelectedGang(null);
        }
        setOpen(change);
    };

    return (
        <Modal
            showClose
            open={open}
            title="Gang Info"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-2xl!"
            contentPadding="px-0 md:px-6 mb-6"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Su197a0.png`}
                    alt=""
                    className="mt-0.5 h-10 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="mx-4 flex flex-col md:mx-12">
                <GangBanner gang={selectedGang} className="-mx-4 h-24 rounded-t-none" />
                <div className="mx-auto my-2 flex w-fit flex-col rounded-lg border border-gray-600 bg-slate-800 p-2 px-4 text-left text-stroke-s-sm text-white">
                    <div className="flex gap-8">
                        <Link to={`/profile/${selectedGang.owner.id}`} className="flex cursor-pointer flex-col">
                            <p className="font-body font-semibold text-gray-300 text-xs uppercase">Gang Leader</p>
                            <div className="flex items-center gap-2">
                                <DisplayAvatar className="size-7 rounded-lg" src={selectedGang.owner} />
                                <p className="text-custom-yellow">{selectedGang.owner.username}</p>
                            </div>
                        </Link>
                        <div className="flex flex-col gap-1">
                            <p className="font-body font-semibold text-gray-300 text-xs uppercase">Created</p>
                            <div className="flex items-center gap-2">
                                <p className="font-body font-semibold text-blue-400 text-sm tracking-wide">
                                    {formatDate(selectedGang.createdAt)}
                                </p>
                            </div>
                        </div>
                    </div>
                    <p className="mt-2 font-body font-semibold text-gray-300 text-xs uppercase">Description</p>
                    <p>{selectedGang.about}</p>
                </div>
                {currentUser?.gangId !== selectedGang?.id && selectedGang?.id !== 1 && !hideInviteButton ? (
                    <>
                        {selectedGang?.requestSent ? (
                            <p className="mx-auto mt-2 w-fit rounded-lg border border-black bg-gray-950 px-4 py-1 text-center text-2xl text-green-500">
                                Request Sent <Check className="mb-0.5 ml-1 inline size-5 text-green-500" />
                            </p>
                        ) : (
                            <button
                                type="button"
                                disabled={selectedGang?.requestSent}
                                className="darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-3 dark:text-slate-200"
                                onClick={() => handleSubmit()}
                            >
                                Request Invite
                            </button>
                        )}
                    </>
                ) : null}
            </div>
        </Modal>
    );
}
