import { useEffect } from "react";
import { useSocketStore } from "../../../app/store/stores";
import Chatbox from "../../chat/Chatbox";
import type { Gang } from "../types/gang";

interface GangChatroomProps {
    gang: Gang;
}

const GangChatroom = ({ gang }: GangChatroomProps) => {
    const { socket } = useSocketStore();
    const currentChatroom = gang.chat_room;

    useEffect(() => {
        if (socket) {
            if (currentChatroom) {
                socket.emit("join room", currentChatroom?.id);
            }
            return () => {
                if (currentChatroom) {
                    socket.emit("leave room", currentChatroom?.id);
                }
            };
        }
    }, [currentChatroom, socket]);

    if (!currentChatroom) return null;
    return (
        <div className="">
            <Chatbox hideHeader fullSize chatRoom={currentChatroom} customHeight="h-[calc(100dvh-20.5rem)]! mt-0!" />
        </div>
    );
};

export default GangChatroom;
