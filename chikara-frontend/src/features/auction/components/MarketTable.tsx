import { AgGridReact } from "ag-grid-react";
import { useState, useRef, useCallback, useEffect } from "react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { usePersistStore } from "@/app/store/stores";
import yenImg from "@/assets/icons/UI/yen.png";
import Button from "@/components/Buttons/Button";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { DisplayItem } from "@/components/DisplayItem";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { rarityColours } from "@/helpers/rarityColours";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { differenceInHours, formatDistanceToNowStrict } from "date-fns";
import { Link } from "react-router-dom";
import type { User } from "@/types/user";
import type { Item } from "@/types/item";
import type { ICellRendererParams } from "ag-grid-community";

interface AuctionItem {
    id: number;
    quantity: number;
    buyoutPrice: number;
    endsAt: string;
    item: Item;
    user: {
        id: number;
        username: string;
        avatar: string;
    };
}

interface DisplayItemCellProps extends ICellRendererParams {
    value: Item;
    data: AuctionItem;
}

const DisplayItemCell = (props: DisplayItemCellProps) => {
    const { value } = props;

    return (
        <div className="relative flex h-full items-center gap-2 py-0.5 md:w-full md:flex-row md:items-start md:gap-4 md:p-1">
            <DisplayItem item={value} className="size-14 md:h-full md:w-auto" />
            <div className="flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0">
                <p
                    className={cn(
                        value.name.length > 15 ? "md:text-sm! text-[0.65rem]" : "text-sm! md:text-base!",
                        "leading-none! text-wrap! truncate text-left font-semibold text-custom-yellow md:text-base"
                    )}
                >
                    {value.name}
                </p>
                <p
                    className={cn(
                        rarityColours(value.rarity),
                        "leading-none! text-xs! md:text-sm! text-left font-semibold"
                    )}
                >
                    {capitaliseFirstLetter(value.rarity)}{" "}
                    <span className="text-gray-200">{capitaliseFirstLetter(value.itemType)}</span>
                </p>
                <Link to={`/profile/${props.data.user.id}`} className="md:hidden! relative mt-1 flex size-full gap-0.5">
                    <DisplayAvatar src={props.data.user} className=" h-4 rounded-full" />
                    <p className="text-xs! text-blue-400">{props.data.user.username}</p>
                </Link>
            </div>
        </div>
    );
};

interface DisplaySellerCellProps extends ICellRendererParams {
    value: {
        id: number;
        username: string;
        avatar: string;
    };
}

const DisplaySellerCell = (props: DisplaySellerCellProps) => {
    const { value } = props;

    return (
        <div className="relative flex size-full py-5">
            <Link to={`/profile/${value.id}`} className="flex gap-2">
                <DisplayAvatar src={value} className="h-full rounded-full" />
                <p className="text-wrap! my-auto font-semibold text-blue-400 text-sm">{value.username}</p>
            </Link>
        </div>
    );
};

interface MarketTableProps {
    auctionList: AuctionItem[];
    setOpenModal: (open: boolean) => void;
    currentUser: User;
    setItemToBuy: (item: AuctionItem) => void;
    setOpenPurchaseItemModal: (open: boolean) => void;
}

const MarketTable = ({ auctionList, setOpenModal, currentUser, setItemToBuy, setOpenPurchaseItemModal }: MarketTableProps) => {
    const gridRef = useRef<AgGridReact>(null);
    const isMobile = useCheckMobileScreen();
    const [currentTab, setCurrentTab] = useState<string>("All");
    const queryClient = useQueryClient();
    const { marketTablePageSize, setMarketTablePageSize } = usePersistStore();

    const handleOpenPurchaseItemModal = (item: AuctionItem) => {
        setItemToBuy(item);
        setOpenPurchaseItemModal(true);
    };

    const cancelListingMutation = useMutation(
        api.auctions.cancelListing.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.auctions.getList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getInventory.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                console.error(error);
                toast.error(error.message);
            },
        })
    );

    const cancelListing = (listingId: number) => {
        const confirm = window.confirm("Are you sure you want to cancel this listing?");
        if (!confirm) return;

        cancelListingMutation.mutate({
            auctionItemId: listingId,
        });
    };

    interface BuyItemCellProps extends ICellRendererParams {
        data: AuctionItem;
    }

    const BuyItemCell = (props: BuyItemCellProps) => {
        const cost = props.data?.buyoutPrice;
        const date = formatDistanceToNowStrict(new Date(props.data?.endsAt));

        return (
            <div className="flex size-full flex-col items-center justify-center md:flex-row md:gap-2">
                {currentUser.id !== props.data.user.id ? (
                    <Button
                        className="font-semibold! w-[95%] md:w-3/4"
                        variant="primary"
                        onClick={() => handleOpenPurchaseItemModal(props.data)}
                    >
                        <div className="flex items-center gap-1 font-body">
                            <img className="size-4" src={yenImg} alt="" />
                            {cost}
                        </div>
                    </Button>
                ) : (
                    <div className="flex size-full flex-col items-center justify-center gap-1">
                        {/* <div className="flex gap-1 font-body text-xs! mx-auto!">
              <img className="h-3 w-3" src={yenImg} alt="" />
              {cost}
            </div> */}
                        <Button
                            className="font-semibold! w-[95%] md:w-3/4"
                            variant="destructive"
                            onClick={() => cancelListing(props.data.id)}
                        >
                            <div className="flex flex-col items-center font-body text-sm md:text-base">
                                <div className="text-xs! -ml-3 mt-0.5 flex gap-1 font-body text-gray-300">
                                    <img my-auto className="my-auto size-3" src={yenImg} alt="" />
                                    {cost}
                                </div>
                                Cancel Listing
                            </div>
                        </Button>
                    </div>
                )}
                <p className="md:hidden! font-body font-semibold text-indigo-500 leading-relaxed">{date} left</p>
            </div>
        );
    };

    function initialFilter() {
        gridRef?.current?.api.setFilterModel({
            user: {
                type: "notEqual",
                filter: currentUser?.id,
            },
        });
    }

    useEffect(() => {
        if (gridRef.current && gridRef.current.api) {
            if (currentTab === "All") {
                gridRef.current.api.setFilterModel({
                    user: {
                        type: "notEqual",
                        filter: currentUser?.id,
                    },
                });
            } else if (currentTab === "CurrentUser") {
                gridRef.current.api.setFilterModel({
                    user: {
                        type: "equals",
                        filter: currentUser?.id,
                    },
                });
            }
        }
    }, [currentTab, currentUser, auctionList]);

    const [colDefs, setColDefs] = useState<any[]>([
        {
            headerName: "Item",
            field: "item",
            cellRenderer: DisplayItemCell,
            minWidth: isMobile ? 183 : null,
            sortable: false,
            getQuickFilterText: (params) => {
                return params.data.item.itemType;
            },
            filter: "agTextColumnFilter",
            filterValueGetter: (params) => {
                return params.data.item.name;
            },
            filterParams: {
                filterOptions: ["contains", "equals", "notEqual", "startsWith", "endsWith"],
                defaultOption: "contains",
            },
        },
        {
            headerName: isMobile ? "Qty" : "Quantity",
            field: "quantity",
            maxWidth: isMobile ? 100 : 125,
            cellClass: "text-base mt-4 font-semibold font-body",
            filter: "agNumberColumnFilter",
            filterParams: {
                filterOptions: ["equals", "greaterThan", "lessThan", "inRange"],
                defaultOption: "equals",
            },
        },
        {
            headerName: "Ends In",
            field: "endsAt",
            cellClass: "text-base font-semibold text-center mt-4",
            hide: isMobile,
            filter: "agNumberColumnFilter",
            floatingFilter: true,
            valueFormatter: (params) => {
                const date = new Date(params.value);
                return formatDistanceToNowStrict(date, { roundingMethod: "ceil" });
            },
            filterValueGetter: (params) => {
                const date = new Date(params.data.endsAt);
                return differenceInHours(date, new Date(), { roundingMethod: "ceil" });
            },
            filterParams: {
                filterOptions: [
                    {
                        displayKey: "equals",
                        displayName: "Equals (hours)",
                        predicate: ([filterValue], cellValue) => {
                            if (cellValue == null) return false;
                            return cellValue === filterValue;
                        },
                    },
                ],
            },
        },
        {
            headerName: "Cost",
            field: "buyoutPrice",
            cellRenderer: BuyItemCell,
            filterParams: {
                filterOptions: ["equals", "greaterThan", "lessThan", "inRange"],
                defaultOption: "equals",
            },
        },
        {
            headerName: "Seller",
            field: "user",
            cellRenderer: DisplaySellerCell,
            hide: isMobile,
            filterValueGetter: (params) => {
                return params.data.user.id;
            },
            filter: true,
            floatingFilter: false,
        },
    ]);

    const defaultColDef = {
        flex: 1,
        sortable: true,
        filter: true,
        resizable: true,
        cellClass: "px-1.5! md:px-2! 2xl:px-6!",
        floatingFilter: true,
        suppressHeaderMenuButton: true,
        suppressMovable: true,
        filterParams: { maxNumConditions: 1 },
    };

    const paginationPageSizeSelector = [10, 30, 50];

    const onPaginationChanged = (event: any) => {
        if (event.api) {
            if (event.newPageSize === true) {
                const pageSize = event.api.paginationGetPageSize();
                if (paginationPageSizeSelector.includes(pageSize)) {
                    if (pageSize !== marketTablePageSize) {
                        setMarketTablePageSize(pageSize);
                    }
                }
            }
        }
    };

    return (
        <div className="ag-theme-quartz-dark rounded-t-lg md:p-2" style={{ width: "100%", overflow: "auto" }}>
            <MarketFilters gridRef={gridRef} setOpenModal={setOpenModal} />
            <MarketTabs setCurrentTab={setCurrentTab} currentTab={currentTab} />

            <AgGridReact
                ref={gridRef}
                suppressCellFocus
                suppressRowHoverHighlight
                pagination
                rowData={auctionList}
                columnDefs={colDefs}
                defaultColDef={defaultColDef}
                domLayout={"autoHeight"}
                rowHeight={80}
                paginationPageSizeSelector={paginationPageSizeSelector}
                paginationPageSize={marketTablePageSize || 10}
                onPaginationChanged={(event) => onPaginationChanged(event)}
                onFirstDataRendered={initialFilter}
            />
        </div>
    );
};

export default MarketTable;

interface MarketFiltersProps {
    gridRef: React.RefObject<AgGridReact>;
    setOpenModal: (open: boolean) => void;
}

const MarketFilters = ({ gridRef, setOpenModal }: MarketFiltersProps) => {
    const onFilterTextBoxChanged = useCallback(() => {
        const filterElement = document.getElementById("filter-item-name-box") as HTMLSelectElement;
        gridRef.current?.api.setGridOption("quickFilterText", filterElement?.value || "");
    }, [gridRef]);

    return (
        <div className="flex w-full items-center gap-4 text-left">
            <div className="w-40 pb-2">
                <span className="ml-3 font-medium font-body text-xs">Item Type</span>
                <select
                    id="filter-item-name-box"
                    className="mt-1 ml-2 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-sm shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:w-52 md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                    onChange={onFilterTextBoxChanged}
                >
                    <option value="">Any</option>
                    <option value="crafting">Crafting Materials</option>
                    <option value="upgrade">Upgrade Materials</option>
                    <option value="weapon">Melee Weapon</option>
                    <option value="ranged">Ranged Weapon</option>
                    <option value="offhand">Offhand</option>
                    <option value="shield">Shield</option>
                    <option value="head">Head</option>
                    <option value="chest">Chest</option>
                    <option value="hands">Gloves</option>
                    <option value="legs">Legs</option>
                    <option value="feet">Feet</option>
                    <option value="finger">Ring</option>
                    <option value="consumable">Consumable</option>
                    <option value="recipe">Recipe</option>
                    <option value="special">Special</option>
                    <option value="pet">Pet</option>
                    <option value="Junk">Junk</option>
                </select>
            </div>
            {/* <div>
        <p className="font-body font-medium text-xs mb-1 -mt-2">Level Range</p>
        <div className="flex gap-1">
          <input
            type="number"
            id="filter-min-level-box"
            name="filter-min-level-box"
            className="block w-full min-w-0 flex-1 text-xs text-center rounded-md border-gray-300 dark:border-gray-500 px-3 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-white"
            placeholder={1}
            min={1}
            step={1}
            max={35}
            onChange={onFilterTextBoxChanged}
          />
          <input
            type="number"
            id="filter-max-level-box"
            name="filter-max-level-box"
            className="block w-full min-w-0 flex-1 text-xs text-center rounded-md border-gray-300 dark:border-gray-500 px-3 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-white"
            placeholder={35}
            min={1}
            step={1}
            max={35}
            onChange={onFilterTextBoxChanged}
          />
        </div>
      </div> */}
            <div className="mr-2 ml-auto flex">
                <Button
                    className="md:text-base! text-xs! md:min-w-32"
                    variant="primary"
                    onClick={() => setOpenModal(true)}
                >
                    Sell Item
                </Button>
            </div>
        </div>
    );
};

interface MarketTabsProps {
    currentTab: string;
    setCurrentTab: (tab: string) => void;
}

const MarketTabs = ({ currentTab, setCurrentTab }: MarketTabsProps) => {
    const marketTabs = [
        { name: "All Listings", value: "All", current: currentTab === "All" },
        { name: "My Listings", value: "CurrentUser", current: currentTab === "CurrentUser" },
    ];
    return (
        <div className="flex w-full">
            {marketTabs.map((tab, tabIdx) => (
                <button
                    key={tab.name}
                    aria-current={tab.current ? "page" : undefined}
                    className={cn(
                        tab.current ? "text-gray-900" : "text-gray-500",
                        tabIdx === 0 ? "rounded-tl-lg" : "rounded-tr-lg",
                        tabIdx === marketTabs.length - 1 ? "" : "",
                        "group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-gray-900 dark:text-white"
                    )}
                    onClick={() => {
                        setCurrentTab(tab.value);
                    }}
                >
                    <span>{tab.name}</span>
                    <span
                        aria-hidden="true"
                        className={cn(
                            tab.current ? "bg-indigo-500" : "bg-transparent",
                            "absolute inset-x-0 bottom-0 h-[0.15rem]"
                        )}
                    />
                </button>
            ))}
        </div>
    );
};
