import { ChangeEvent } from "react";

interface FilterModel {
    type: string;
    filter: string;
}

interface ClassFilterProps {
    model: FilterModel | null;
    onModelChange: (model: FilterModel | null) => void;
}

const ClassFilter = ({ model, onModelChange }: ClassFilterProps) => {
    const value = (model && model.filter) || "";

    const onChange = ({ target: { value: newValue } }: ChangeEvent<HTMLSelectElement>) => {
        onModelChange(
            newValue === "" || !newValue
                ? null
                : {
                      ...(model || {
                          type: "equals",
                      }),
                      filter: newValue,
                  }
        );
    };

    return (
        <div className="ag-floating-filter-input-wrapper">
            <select
                value={value}
                className="ag-input-field-input ag-text-field-input rounded-md border-gray-600 font-bold text-sm focus:border-[#2196f3] lg:py-1 lg:text-base"
                onChange={onChange}
            >
                <option value="">All</option>
                <option value="mizu">Mizu</option>
                <option value="honoo">Honoo</option>
                <option value="tsuchi">Tsuchi</option>
                <option value="kaze">Kaze</option>
            </select>
        </div>
    );
};

export default ClassFilter;
