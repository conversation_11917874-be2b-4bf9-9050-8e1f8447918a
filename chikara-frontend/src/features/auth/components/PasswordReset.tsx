import { useState } from "react";
import { Link, useSearchParams } from "react-router-dom";

import { resetPassword } from "@/lib/auth-client";
import { toast } from "react-hot-toast";

export default function PasswordReset() {
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [resetSuccess, setResetSuccess] = useState(false);
    const [searchParams] = useSearchParams();

    const token = searchParams.get("token");
    // Do more checks to see if token is valid (length etc)

    const handleReset = async (e: React.FormEvent) => {
        e.preventDefault();
        if (password !== confirmPassword) {
            toast.error("Passwords are not the same!");
            return;
        }
        if (password.length < 6) {
            toast.error("Password is too short!");
            return;
        }

        const { data, error } = await resetPassword({
            newPassword: password,
            token: token || "",
        });

        if (data && !error) {
            console.log(data);
            setResetSuccess(true);
        } else {
            // eslint-disable-next-line no-lonely-if
            if (error.message) {
                toast.error(error.message);
            } else {
                toast.error("Server error! Try again later");
            }
        }
    };

    return (
        <>
            {token === null ? (
                <div className="px-4 pt-3 pb-6 shadow-sm sm:px-10 md:py-8">
                    <div className="sm:mx-auto sm:w-full sm:max-w-md">
                        <h3 className="mb-5 text-center text-gray-200 text-xl lg:text-3xl">Invalid Token</h3>
                        <p className="text-center text-sm md:text-sm lg:text-lg">
                            Please try to{" "}
                            <Link to="/forgotpassword" className="cursor-pointer text-blue-600">
                                reset your password
                            </Link>{" "}
                            again.
                        </p>
                    </div>
                </div>
            ) : (
                <>
                    {resetSuccess ? (
                        <div className="px-4 pt-3 pb-6 shadow-sm sm:px-10 md:py-8">
                            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                                <h2 className="mb-5 text-center font-bold text-3xl text-blue-600">Success</h2>
                                <p className="text-center text-lg">Your password was changed successfully!</p>
                                <Link to="/login">
                                    <button className="mt-5 flex w-full justify-center rounded-md border border-transparent bg-sky-600 px-4 py-2 font-medium text-shadow text-sm text-white shadow-xs hover:bg-sky-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                        Back to Login
                                    </button>
                                </Link>
                            </div>
                        </div>
                    ) : (
                        <div className="px-4 pt-3 pb-6 shadow-sm sm:px-10 md:py-8">
                            <form className="space-y-5" action="#" method="POST">
                                <div>
                                    <label htmlFor="password" className="block font-medium text-gray-300 text-sm">
                                        New password
                                    </label>
                                    <div className="mt-1">
                                        <input
                                            required
                                            type="password"
                                            name="password"
                                            autoComplete="password"
                                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                                            onChange={(e) => {
                                                setPassword(e.target.value);
                                            }}
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label
                                        htmlFor="confirmPassword"
                                        className="block font-medium text-gray-300 text-sm"
                                    >
                                        Confirm password
                                    </label>
                                    <div className="mt-1">
                                        <input
                                            required
                                            type="password"
                                            name="confirmPassword"
                                            autoComplete="password"
                                            className="block w-full appearance-none rounded-md border border-gray-600 bg-gray-800 px-3 py-2 text-gray-200 shadow-xs placeholder:text-gray-500 focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"
                                            onChange={(e) => {
                                                setConfirmPassword(e.target.value);
                                            }}
                                        />
                                    </div>
                                </div>

                                <div>
                                    <button
                                        className="flex w-full justify-center rounded-md border border-transparent bg-sky-600 px-4 py-2 font-medium text-shadow text-sm text-white shadow-xs hover:bg-sky-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                        onClick={(e) => {
                                            handleReset(e);
                                        }}
                                    >
                                        Continue
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}
                </>
            )}
        </>
    );
}
