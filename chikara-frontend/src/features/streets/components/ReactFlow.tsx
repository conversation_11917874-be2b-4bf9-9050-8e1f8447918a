import ELK from "elkjs/lib/elk.bundled.js";
import React, { useLayoutEffect, useMemo, useCallback } from "react";
import React<PERSON>low, { ReactFlowProvider, Panel, useNodesState, useEdgesState, Node, Edge } from "reactflow";
import ReactFlowNode from "./ReactFlowNode";

import "reactflow/dist/style.css";

const proOptions = { hideAttribution: true };
const elk = new ELK();

const elkOptions = {
    "elk.direction": "UP",
    "elk.algorithm": "layered",
    "elk.layered.spacing.nodeNodeBetweenLayers": "65",
    "elk.spacing.nodeNode": "60",
};

interface MapNode {
    id: number;
    encounterType: string;
    isNextNode?: boolean;
    complete?: boolean;
    playerHere?: boolean;
}

interface MapEdge {
    source: number;
    target: number;
}

interface FutureNode {
    id: number;
}

interface MapData {
    nodes: MapNode[];
    edges: MapEdge[];
    futureNodes: FutureNode[];
    playerLocation: number;
}

interface LayoutFlowProps {
    data: MapData;
    advanceNode: (nodeId: string) => void;
    onClickNode: (nodeId: string) => void;
}

interface ReactFlowWrapperProps {
    data: MapData;
    advanceNode: (nodeId: string) => void;
    onClickNode: (nodeId: string) => void;
}

const getLayoutedElements = (nodes: Node[], edges: Edge[] = []) => {
    const graph = {
        id: "root",
        layoutOptions: elkOptions,
        children: nodes.map((node) => ({
            ...node,
            targetPosition: "bottom",
            sourcePosition: "top",

            // Hardcode a width and height for elk to use when layouting.
            width: 40,
            height: 60,
        })),
        edges: edges,
    };

    return elk
        .layout(graph)
        .then((layoutedGraph) => ({
            nodes: layoutedGraph.children.map((node) => ({
                ...node,
                // React Flow expects a position property on the node instead of `x`
                // and `y` fields.
                position: {
                    x: (node.data as any)?.encounterType === "base" ? 250 : node.x,
                    y: node.y,
                },
            })),

            edges: layoutedGraph.edges,
        }))
        .catch(console.error);
};

function LayoutFlow({ data, advanceNode, onClickNode }: LayoutFlowProps) {
    const nodeTypes = useMemo(() => ({ customNode: ReactFlowNode }), []);

    const convertedNodes = useMemo(() => {
        return (
            data?.nodes?.map((node) => {
                const isFutureNode = data?.futureNodes.find((futureNode) => futureNode.id === node.id);
                return {
                    id: node?.id?.toString(),
                    type: "customNode",
                    data: {
                        advanceNode: advanceNode,
                        onClickNode: onClickNode,
                        encounterType: node?.encounterType,
                        isFutureNode: isFutureNode,
                        isNextNode: node?.isNextNode,
                        complete: node.complete,
                        playerHere: node?.playerHere,
                    },
                    sourcePosition: "bottom",
                    position: { x: 0, y: 0 }, // Initial position, will be updated by layout
                };
            }) || []
        );
    }, [data.nodes, data.futureNodes, advanceNode, onClickNode]);

    const convertedEdges = useMemo(() => {
        return (
            data?.edges?.map((edge, index) => {
                const isPlayerHere = edge?.source === data?.playerLocation;
                const isFutureNode = data?.futureNodes.find((futureNode) => futureNode.id === edge?.source);

                const edgeOpacity = isFutureNode || isPlayerHere ? 1 : 0.3;
                return {
                    id: index?.toString(),
                    source: edge?.source?.toString(),
                    target: edge?.target?.toString(),
                    type: "straight",
                    style: { stroke: "black", strokeWidth: 3, opacity: edgeOpacity },
                };
            }) || []
        );
    }, [data.edges, data.futureNodes, data.playerLocation]);

    const [nodes, setNodes] = useNodesState([]);
    const [edges, setEdges] = useEdgesState([]);

    const onLayout = useCallback(() => {
        getLayoutedElements(convertedNodes, convertedEdges).then((result) => {
            if (result) {
                const { nodes: layoutedNodes, edges: layoutedEdges } = result;
                setNodes(layoutedNodes);
                setEdges(layoutedEdges);
            }
        });
    }, [convertedNodes, convertedEdges]);

    // Calculate the initial layout on mount.
    useLayoutEffect(() => {
        onLayout();
    }, [onLayout]);

    return (
        <ReactFlow
            fitView
            nodes={nodes}
            edges={edges}
            proOptions={proOptions}
            nodeTypes={nodeTypes}
            panOnDrag={false}
            zoomOnScroll={false}
            zoomOnDoubleClick={false}
            zoomOnPinch={false}
        ></ReactFlow>
    );
}

export default ({ data, advanceNode, onClickNode }: ReactFlowWrapperProps) => (
    <ReactFlowProvider>
        <LayoutFlow data={data} advanceNode={advanceNode} onClickNode={onClickNode} />
    </ReactFlowProvider>
);
