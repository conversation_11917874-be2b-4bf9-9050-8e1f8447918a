import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { AnimatePresence, motion } from "framer-motion";
import { useLayoutEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSessionStore } from "../../../app/store/stores";
import EncounterView from "./EncounterView";
import MapView from "./MapView";
import ScavengeView from "./ScavengeView";
import { type User } from "@/types/user";

interface StreetsProps {
    currentMap: User["roguelikeMap"];
}

export default function Streets({ currentMap }: StreetsProps) {
    const navigate = useNavigate();
    const { inEncounter } = useSessionStore();
    const { ROGUELIKE_DISABLED } = useGameConfig();
    const { data: currentUser } = useFetchCurrentUser();

    const showScavengeView =
        currentMap?.nodes?.[currentMap.playerLocation]?.encounterType === "scavenge" && currentMap?.currentNodeChoices;

    useLayoutEffect(() => {
        if (currentMap?.currentNodeComplete === false) {
            const currentNode = currentMap?.nodes?.[currentMap.playerLocation];
            if (currentNode && (currentNode.encounterType === "battle" || currentNode.encounterType === "boss")) {
                navigate("/fight");
            }
        }
    }, [currentMap?.playerLocation, currentMap?.currentNodeComplete, navigate]);

    if (ROGUELIKE_DISABLED && currentUser?.userType !== "admin")
        return (
            <div className="mt-80 flex flex-col lg:mt-64 2xl:mt-80 dark:text-slate-200">
                <div className="mx-auto rounded-lg border-2 border-gray-600 bg-black/75 p-6 text-center">
                    <h2 className="text-xl 2xl:text-3xl">Streets is currently Disabled</h2>
                    <p className="text-base text-gray-300 2xl:text-xl">Please return later.</p>
                </div>
            </div>
        );

    return (
        <>
            <AnimatePresence mode="wait">
                {inEncounter || showScavengeView ? (
                    <div className="flex h-[calc(100dvh-8.75rem)] border border-slate-600 bg-black md:mx-auto md:mt-8 md:h-full md:max-w-(--breakpoint-2xl) md:rounded-lg md:border-2">
                        {inEncounter ? (
                            <motion.div key="encounterView" className="flex h-full" exit={{ opacity: 0 }}>
                                <EncounterView currentMap={currentMap} />
                            </motion.div>
                        ) : (
                            <motion.div key="scavengeView" className="flex h-full" exit={{ opacity: 0 }}>
                                <ScavengeView currentMap={currentMap} />
                            </motion.div>
                        )}
                    </div>
                ) : (
                    <div className="h-full md:mx-auto md:mt-4 md:w-5/6">
                        <motion.div key="mapView" className="h-full" exit={{ opacity: 0, backgroundColor: "black" }}>
                            <MapView currentMap={currentMap} />
                        </motion.div>
                    </div>
                )}
            </AnimatePresence>
        </>
    );
}
