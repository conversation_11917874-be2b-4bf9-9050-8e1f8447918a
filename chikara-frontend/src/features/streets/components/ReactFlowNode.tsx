import searchIcon from "@/assets/icons/navitems/search.png";
import youAreHere from "@/assets/images/youAreHere.svg";
import { cn } from "@/lib/utils";
import { Handle, Position } from "reactflow";

const bottomHandleStyle = {
    bottom: 35,
    color: "transparent",
    background: "transparent",
    cursor: "auto",
    zIndex: -1,
    border: "none",
};

const topHandleStyle = {
    top: 40,
    color: "transparent",
    background: "transparent",
    cursor: "auto",
    zIndex: -1,
    border: "none",
};

function generateSymbols(encounterType: string): string | null {
    if (encounterType === "buff") {
        return "https://img.icons8.com/plasticine/100/chevron.png";
    }
    if (encounterType === "base") {
        return import.meta.env.VITE_IMAGE_CDN_URL + "/ui-images/ZI53gYm.png" || null;
    }
    if (encounterType === "boss") {
        return import.meta.env.VITE_IMAGE_CDN_URL + "/ui-images/hP5gDWq.png" || null;
    }
    if (encounterType === "character") {
        return import.meta.env.VITE_IMAGE_CDN_URL + "/ui-images/591AjOU.png" || null;
    }
    if (encounterType === "battle") {
        return import.meta.env.VITE_IMAGE_CDN_URL + "/ui-images/xH8pCG3.png" || null;
    }
    if (encounterType === "scavenge") {
        return searchIcon || null;
    }
    return null;
}

interface ReactFlowNodeData {
    complete?: boolean;
    playerHere?: boolean;
    isFutureNode?: boolean;
    isNextNode?: boolean;
    encounterType: string;
    onClickNode: (id: string) => void;
}

interface ReactFlowNodeProps {
    data: ReactFlowNodeData;
    id: string;
}

export default function ReactFlowNode({ data, id }: ReactFlowNodeProps) {
    const nodeIsComplete = data?.complete;
    const playerIsHere = data?.playerHere;
    let isFutureNode = data?.isFutureNode;
    const isNextNode = data?.isNextNode;

    if (data.encounterType === "base") {
        if (playerIsHere) {
            isFutureNode = true;
        }
    }

    const handleClick = (id: string) => {
        if (isNextNode) {
            data.onClickNode(id);
        }
        return;
    };
    
    return (
        <>
            <Handle type="target" position={Position.Top} style={topHandleStyle} />
            <div
                className={`nodrag ${!isNextNode ? "cursor-auto" : "cursor-pointer"} `}
                onClick={() => handleClick(id)}
            >
                {playerIsHere && (
                    <img
                        src={youAreHere}
                        className="bounce pointer-events-none absolute bottom-12 left-3 z-100 h-10 w-16 text-indigo-700 md:h-12 md:w-auto"
                        alt="You are here"
                    />
                )}
                {nodeIsComplete && <div className="cross absolute top-4 left-3.5 z-50"></div>}

                <img
                    src={generateSymbols(data.encounterType)}
                    alt=""
                    className={cn(
                        "mt-3 h-16 w-auto",
                        !isFutureNode ? "scale-[0.8] opacity-50 grayscale" : null,
                        isNextNode && "hover:scale-125",
                        !isNextNode && isFutureNode ? "scale-[0.8]" : null
                    )}
                />
            </div>
            <Handle type="source" position={Position.Bottom} style={bottomHandleStyle} />
        </>
    );
}
