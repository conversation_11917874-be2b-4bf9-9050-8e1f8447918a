import craftImg from "@/assets/images/UI/Tasks/craft.png";
import fetchImg from "@/assets/images/UI/Tasks/fetch.png";
import npcKillImg from "@/assets/images/UI/Tasks/npc_kill.png";
import pvpKillImg from "@/assets/images/UI/Tasks/pvp_kill.png";
import uniqueImg from "@/assets/images/UI/Tasks/unique.png";
import type { QuestWithProgress } from "@/types/quest";

export const getQuestTypeIcon = (quest: QuestWithProgress | null | undefined): string | undefined => {
    if (!quest) return;

    // Get the objective type from the first quest objective
    const objectiveType = quest.quest_objective?.[0]?.objectiveType || "UNIQUE_OBJECTIVE";

    switch (objectiveType) {
        case "DEFEAT_NPC":
        case "DEFEAT_NPC_IN_TURNS":
        case "DEFEAT_NPC_WITH_LOW_DAMAGE":
            return npcKillImg;
        case "DEFEAT_PLAYER":
        case "PVP_POST_BATTLE_CHOICE":
        case "PLACE_BOUNTY":
            return pvpKillImg;
        case "UNIQUE_OBJECTIVE":
            return uniqueImg;
        case "ACQUIRE_ITEM":
            return fetchImg;
        case "CRAFT_ITEM":
            return craftImg;
        default:
            return uniqueImg;
    }
};

interface ShopkeeperData {
    id: number;
    name: string;
    shopType: "food" | "weapon" | "armour" | "general" | "furniture";
    avatar: string;
    description: string;
    disabled: boolean | null;
}

export const shopkeeperMockData: ShopkeeperData[] = [
    {
        id: 1,
        name: "Nagao",
        shopType: "food",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Nagao/happyopen.webp",
        description: "Nagao's food shop",
        disabled: null,
    },
    {
        id: 2,
        name: "Goda",
        shopType: "weapon",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Goda/happyopen.webp",
        description: "Goda's weapon shop",
        disabled: null,
    },
    {
        id: 3,
        name: "Mihara",
        shopType: "armour",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Mihara/happy.webp",
        description: "Mihara's armor shop",
        disabled: null,
    },
    {
        id: 4,
        name: "Shoko",
        shopType: "general",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Shoko/happyopen.webp",
        description: "Shoko's general shop",
        disabled: null,
    },
    {
        id: 5,
        name: "Otake",
        shopType: "general",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Otake/neutral.webp",
        description: "???",
        disabled: null,
    },
    {
        id: 6,
        name: "Honda",
        shopType: "furniture",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/characters/Honda/neutral.webp",
        description: "???",
        disabled: true,
    },
];

interface AnimationVariants {
    animate: {
        x: number;
        opacity: number;
        transition: {
            duration: number;
        };
    };
    initial: (direction: "left" | "right") => {
        x: string;
        opacity: number;
    };
    exit: (direction: "left" | "right") => {
        x: string;
        opacity: number;
        transition: {
            duration: number;
        };
    };
}

export const animationVariantsMobile: AnimationVariants = {
    animate: {
        x: 0,
        opacity: 1,

        transition: {
            duration: 0.2,
        },
    },
    initial: (direction: "left" | "right") => {
        return {
            x: direction === "right" ? "50%" : "-50%",
            opacity: 0,
        };
    },
    exit: (direction: "left" | "right") => {
        return {
            x: direction === "right" ? "-50%" : "50%",
            opacity: 0,
            transition: {
                duration: 0.2,
            },
        };
    },
};
