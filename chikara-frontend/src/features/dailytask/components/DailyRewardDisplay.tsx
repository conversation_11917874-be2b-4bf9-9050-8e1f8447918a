import expicon from "@/assets/icons/UI/expicon.png";
import yenImg from "@/assets/icons/UI/yen.png";
import blueBackground from "@/assets/icons/frames/enhanced.png";
import greenBackground from "@/assets/icons/frames/standard.png";
import { DisplayItem } from "@/components/DisplayItem";
import type { Item } from "@/types/item";

interface DailyRewardDisplayProps {
    quest: {
        itemRewardId?: number;
        item?: Item;
        itemRewardQuantity?: number;
        quantity?: number;
        cashReward?: number;
        xpReward?: number;
    };
}

const DailyRewardDisplay = ({ quest }: DailyRewardDisplayProps) => {
    if (quest.itemRewardId && quest.item) {
        return (
            <div className="h-12">
                <DisplayItem item={quest.item} quantity={quest.itemRewardQuantity} />
            </div>
        );
    }

    let rewardImg: string = "";
    let rewardAltText: string = "";
    let backgroundImg: string = "";
    let quantity: number | undefined = quest.quantity;

    if (quest?.cashReward && quest?.cashReward > 0) {
        rewardImg = yenImg;
        rewardAltText = "Yen";
        backgroundImg = greenBackground;
        quantity = quest?.cashReward;
    }

    if (quest?.xpReward && quest?.xpReward > 0) {
        rewardImg = expicon;
        rewardAltText = "EXP";
        backgroundImg = blueBackground;
        quantity = quest?.xpReward;
    }

    return (
        <div data-tooltip-id="questreward-tooltip" data-tooltip-content={rewardAltText} className="relative w-auto">
            <img src={backgroundImg} className="h-full w-auto bg-cover" alt="" />

            <img
                className="-translate-x-1/2 -translate-y-1/2 absolute top-[47%] left-1/2 z-5 h-12 "
                src={rewardImg}
                alt={`${rewardAltText}`}
            />
            {quantity && (
                <p className="absolute right-2 bottom-2 z-10 text-stroke-s-sm text-xs text-gray-200">{quantity}</p>
            )}
        </div>
    );
};

export default DailyRewardDisplay;
