import DisplayShopItem from "@/components/Items/DisplayShopItem";
import { formatCurrency } from "@/utils/currencyHelpers";
import type { InventoryItem } from "@/types/item";

interface ShopItemSellProps {
    item: InventoryItem | null;
    setOpenModal: (open: boolean) => void;
    setItemToSell: (item: InventoryItem) => void;
}

function ShopItemSell({ item, setOpenModal, setItemToSell }: ShopItemSellProps) {
    if (!item) return null;
    return (
        <div
            key={item.id}
            className="group cursor-pointer divide-gray-200 rounded-lg shadow-sm"
            onClick={() => {
                setItemToSell(item);
                setOpenModal(true);
            }}
        >
            <DisplayShopItem item={item?.item} type="sell" count={item?.count} />

            <div className="-mt-1.5 flex flex-col">
                <div className="relative mx-auto flex w-[90%] items-center justify-center rounded-b-md border border-transparent bg-gray-100 pt-3 pb-2 font-medium text-base text-gray-900 ring-2 ring-black group-hover:bg-gray-800 md:text-base dark:border dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 dark:group-hover:border-gray-400">
                    <span className="mr-0.5 text-green-500">+</span>
                    {formatCurrency(item?.item?.cashValue)}
                </div>
            </div>
        </div>
    );
}

export default ShopItemSell;
