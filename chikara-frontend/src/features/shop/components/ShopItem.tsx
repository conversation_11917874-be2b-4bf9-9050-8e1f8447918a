import gangCredImg from "@/assets/icons/UI/currency2.png";
import DisplayShopItem from "@/components/Items/DisplayShopItem";
import { cn } from "@/lib/utils";
import { getCurrencySymbol } from "@/utils/currencyHelpers";
import type { ShopListing, CurrencyType } from "../types/shop";

interface ShopItemProps {
    product: ShopListing;
    setOpenModal: (open: boolean) => void;
    setItemToBuy: (item: ShopListing) => void;
    currencyType?: CurrencyType;
}

const displayCurrency = (currencyType: CurrencyType) => {
    if (currencyType === "yen") {
        return getCurrencySymbol("yen");
    }
    if (currencyType === "gangCreds") {
        return <img src={gangCredImg} alt="" className="mx-0.5 mb-0.5 inline h-6 w-auto" />;
    }
    if (currencyType === "classPoints") {
        return (
            <img
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Ou05yxV.png`}
                alt=""
                className="mr-1 mb-0.5 ml-0.5 inline h-6 w-auto"
            />
        );
    }

    return getCurrencySymbol("yen");
};

function ShopItem({ product, setOpenModal, setItemToBuy, currencyType = "yen" }: ShopItemProps) {
    return (
        <div
            className="group cursor-pointer divide-gray-200 rounded-lg shadow-sm"
            onClick={() => {
                setItemToBuy(product);
                setOpenModal(true);
            }}
        >
            <DisplayShopItem item={product.item} />
            <div className="-mt-1.5 flex flex-col">
                <div className="relative mx-auto flex w-[90%] flex-col items-center justify-center rounded-b-md border border-transparent bg-gray-100 pt-3 pb-2 font-medium text-base text-gray-900 ring-2 ring-black group-hover:bg-gray-800 md:text-lg dark:border dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 dark:group-hover:border-gray-400">
                    <p>
                        {displayCurrency(currencyType)}
                        {product.customCost > 0 ? product.customCost : product.cost}
                    </p>
                    {product.stock !== null && (
                        <p className={cn("text-xs", product.stock === 0 ? "text-red-500" : "text-green-500")}>
                            <span className={cn("text-xs", product.stock === 0 ? "text-red-500" : "text-green-500")}>
                                {product.stock}{" "}
                            </span>
                            Remaining
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
}

export default ShopItem;
