import type { AppRouterClient } from "@/lib/orpc";

export type Shop = Awaited<ReturnType<AppRouterClient["shop"]["shopList"]>>[number];
export type ShopInfo = Awaited<ReturnType<AppRouterClient["shop"]["shopInfo"]>>;
export type ShopListing = ShopInfo["shop_listing"][number];

export type AuctionList = Awaited<ReturnType<AppRouterClient["auction"]["getList"]>>;
export type AuctionItem = AuctionList[number];

export type CurrencyType = "yen" | "gangCreds" | "classPoints";

export type ShopType = "general" | "weapon" | "armour" | "food" | "furniture" | "gang";
