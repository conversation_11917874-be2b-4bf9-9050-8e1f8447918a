import buffImg from "@/assets/animations/buffAnim.gif";
import healImg from "@/assets/animations/healAnim.gif";
import physDmgAbilityImg from "@/assets/animations/skillAttackAnim2.gif";
import clsx from "clsx";
import { useEffect, useState } from "react";

type AnimationType = "css" | "gif" | "text";

interface AnimationConfig {
    type: AnimationType;
    className: string;
    duration: number;
    path?: string;
    containerClassName?: string;
}

type AnimationKey =
    | "ranged"
    | "slash"
    | "physDmgAbility"
    | "heal"
    | "buff"
    | "damageText"
    | "healText"
    | "bleedText"
    | "fleeSuccessText"
    | "fleeFailText";

const animations: Record<AnimationKey, AnimationConfig> = {
    ranged: {
        type: "css",
        className: "ranged-attack-animation fixed -top-5 right-10",
        duration: 750,
    },
    slash: {
        type: "css",
        className: "slash fixed -top-1 right-10",
        duration: 750,
    },
    physDmgAbility: {
        type: "gif",
        path: physDmgAbilityImg,
        className: "",
        duration: 500,
    },
    heal: {
        type: "gif",
        path: healImg,
        className: "scale-[2]",
        containerClassName: "top-[75%]",
        duration: 1000,
    },
    buff: {
        type: "gif",
        path: buffImg,
        className: "opacity-50",
        duration: 750,
    },
    damageText: {
        type: "text",
        duration: 3000,
        className: "damageText",
        containerClassName: "left-[65%]",
    },
    healText: {
        type: "text",
        duration: 3000,
        className: "healText",
        containerClassName: "left-[65%]",
    },
    bleedText: {
        type: "text",
        duration: 3000,
        className: "bleedText",
        containerClassName: "left-[55%]",
    },
    fleeSuccessText: {
        type: "text",
        duration: 800,
        className: "fleeSuccessText w-full text-4xl md:text-5xl",
        containerClassName: "w-fit! fixed! left-[48%] top-[80%] z-50!",
    },
    fleeFailText: {
        type: "text",
        duration: 800,
        className: "fleeFailText w-full text-4xl md:text-5xl",
        containerClassName: "w-fit! fixed! left-[48%] top-[80%] z-50!",
    },
};

interface BattleAnimationControllerProps {
    animationKey: AnimationKey;
    animationComplete: () => void;
    play: boolean;
    value?: string | number;
}

const BattleAnimationController = ({ animationKey, animationComplete, play, value }: BattleAnimationControllerProps) => {
    const [animation, setAnimation] = useState<AnimationConfig | null>(null);

    useEffect(() => {
        if (!play) return;
        setAnimation(animations[animationKey]);
        if (animations[animationKey]?.type === "gif") {
            setTimeout(() => {
                animationComplete();
            }, animations[animationKey]?.duration);
        }
    }, [animationKey, play, animationComplete]);

    if (!play) return null;

    if (!animation) return null;

    const renderAnimation = () => {
        switch (animation.type) {
            case "gif":
                return <img className={animation.className} src={animation.path} alt="animation" />;
            case "css":
                return <div className={animation.className} onAnimationEnd={() => animationComplete()}></div>;
            case "text":
                return (
                    <div className={animation.className} onAnimationEnd={() => animationComplete()}>
                        {value}
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div
            className={clsx(
                "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none",
                animation.containerClassName
            )}
        >
            {renderAnimation()}
        </div>
    );
};

export default BattleAnimationController;
